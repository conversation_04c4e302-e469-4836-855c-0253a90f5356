/* eslint-disable no-console */
import { <PERSON><PERSON><PERSON>, BrowserContext, webkit } from '@playwright/test'

/**
 * WebKit browser factory with enhanced stability features
 * Implements retry logic and health checks to prevent browser crashes
 */
export class WebKitBrowserFactory {
  private static readonly MAX_RETRIES = 3

  private static readonly RETRY_DELAY = 2000

  private static readonly HEALTH_CHECK_TIMEOUT = 5000

  /**
   * Launch WebKit browser with retry logic
   */
  static async launch(options: Record<string, unknown> = {}): Promise<Browser> {
    const defaultOptions = {
      timeout: 300000,
      slowMo: 500,
      headless: true,
      args: [
        // WebKit doesn't support many Chrome/Chromium flags
        // Only include WebKit-compatible args
      ],
      env: {
        ...process.env,
        WEBKIT_DISABLE_COMPOSITING: '1',
        WEBKIT_FORCE_COMPOSITING_MODE: '0',
      },
    }

    const mergedOptions = { ...defaultOptions, ...options }

    let lastError: Error | null = null

    for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
      try {
        console.log(
          `🔧 Launching WebKit browser (attempt ${attempt}/${this.MAX_RETRIES})...`
        )

        // eslint-disable-next-line no-await-in-loop
        const browser = await webkit.launch(mergedOptions)

        // Verify browser is healthy
        // eslint-disable-next-line no-await-in-loop
        const isHealthy = await this.checkBrowserHealth(browser)
        if (!isHealthy) {
          throw new Error('Browser health check failed')
        }

        console.log('✅ WebKit browser launched successfully')
        return browser
      } catch (error) {
        lastError = error as Error
        console.error(`❌ WebKit launch failed (attempt ${attempt}):`, error)

        if (attempt < this.MAX_RETRIES) {
          console.log(`🔄 Retrying in ${this.RETRY_DELAY}ms...`)
          // eslint-disable-next-line no-await-in-loop
          await this.sleep(this.RETRY_DELAY)
        }
      }
    }

    throw new Error(
      `Failed to launch WebKit after ${this.MAX_RETRIES} attempts: ${lastError}`
    )

    throw new Error('Unexpected error in WebKit browser launch')
  }

  /**
   * Create browser context with stability options
   */
  static async createContext(
    browser: Browser,
    options: Record<string, unknown> = {}
  ): Promise<BrowserContext> {
    const defaultOptions = {
      viewport: { width: 390, height: 844 },
      reducedMotion: 'reduce',
      forcedColors: 'none',
      colorScheme: 'light',
      serviceWorkers: 'block',
      locale: 'en-US',
      permissions: [],
      timeout: 60000,
    }

    const mergedOptions = { ...defaultOptions, ...options }

    try {
      const context = await browser.newContext(mergedOptions)

      // Add event handlers for stability
      context.on('page', (page) => {
        page.on('crash', () => {
          console.error('⚠️ Page crashed!')
        })

        page.on('pageerror', (error) => {
          console.error('⚠️ Page error:', error)
        })
      })

      return context
    } catch (error) {
      console.error('❌ Failed to create browser context:', error)
      throw error
    }
  }

  /**
   * Check if browser is healthy and responsive
   */
  private static async checkBrowserHealth(browser: Browser): Promise<boolean> {
    try {
      const context = await browser.newContext({
        viewport: { width: 390, height: 844 },
      })

      const page = await context.newPage()

      // Set a timeout for the health check
      const healthCheckPromise = page.goto(
        'data:text/html,<html><body>Health Check</body></html>',
        {
          timeout: this.HEALTH_CHECK_TIMEOUT,
        }
      )

      await healthCheckPromise

      const content = await page.textContent('body')
      const isHealthy = content === 'Health Check'

      await page.close()
      await context.close()

      return isHealthy
    } catch (error) {
      console.error('❌ Browser health check failed:', error)
      return false
    }
  }

  /**
   * Gracefully close browser with cleanup
   */
  static async closeBrowser(browser: Browser): Promise<void> {
    try {
      if (browser && browser.isConnected()) {
        console.log('🧹 Closing WebKit browser...')
        await browser.close()
        console.log('✅ WebKit browser closed successfully')
      }
    } catch (error) {
      console.error('⚠️ Error closing browser:', error)
      // Force kill if graceful close fails
      try {
        if (browser) {
          await browser.close()
        }
      } catch {
        // Ignore secondary errors
      }
    }
  }

  /**
   * Sleep utility
   */
  private static sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }
}
