import React from 'react'
import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ExerciseSetsGrid } from '../ExerciseSetsGrid'
import type {
  ExerciseModel,
  RecommendationModel,
  WorkoutLogSerieModel,
} from '@/types'
import type { WorkoutLogSerieModelRef } from '@/types/api/WorkoutLogSerieModelRef'

// Mock the NavigationContext
vi.mock('@/contexts/NavigationContext', () => ({
  useNavigation: () => ({
    setTitle: vi.fn(),
    setShowBackButton: vi.fn(),
  }),
}))

describe('ExerciseSetsGrid - Individual Set Type Display', () => {
  const mockExercise: ExerciseModel = {
    Id: 1,
    Label: 'Bench Press',
    IsBodyweight: false,
    IsSystemExercise: true,
    IsSwapTarget: false,
    IsFinished: false,
    IsUnilateral: false,
    IsTimeBased: false,
    IsEasy: false,
    IsMedium: true,
    VideoUrl: '',
    IsNextExercise: false,
    IsPlate: true,
    IsWeighted: true,
    IsPyramid: true,
    IsNormalSets: false,
    Timer: 0,
    LocalVideo: '',
    IsAssisted: false,
  }

  const mockPyramidRecommendation: RecommendationModel = {
    ExerciseId: 1,
    Series: 3,
    Reps: 10,
    Weight: { Lb: 135, Kg: 61.23 },
    WarmupsCount: 0,
    IsPyramid: true,
  }

  it('should display individual set type badges based on SetTitle', () => {
    // Create sets with different types based on SetTitle
    const mixedSets: (WorkoutLogSerieModel &
      Partial<WorkoutLogSerieModelRef>)[] = [
      {
        Id: -1001,
        SetNo: '1',
        Reps: 10,
        Weight: { Lb: 135, Kg: 61.23 },
        IsFinished: false,
        IsNext: true,
        IsWarmups: false,
        SetTitle: 'Pyramid set:',
      },
      {
        Id: -1002,
        SetNo: '2',
        Reps: 8,
        Weight: { Lb: 145, Kg: 65.77 },
        IsFinished: false,
        IsNext: false,
        IsWarmups: false,
        SetTitle: '', // Pyramid sets after first don't have title
      },
      {
        Id: -1003,
        SetNo: '3',
        Reps: 10,
        Weight: { Lb: 115, Kg: 52.16 },
        IsFinished: false,
        IsNext: false,
        IsWarmups: false,
        SetTitle: 'Back-off set:',
        IsBackOffSet: true,
      },
    ]

    render(
      <ExerciseSetsGrid
        exercise={mockExercise}
        recommendation={mockPyramidRecommendation}
        sets={mixedSets}
        onSetUpdate={vi.fn()}
      />
    )

    // Should show Pyramid badge for first set
    const pyramidBadges = screen.getAllByText('Pyramid')
    expect(pyramidBadges).toHaveLength(1) // Only first pyramid set has badge

    // Should show Back-off badge for third set
    const backoffBadge = screen.getByText('Back-off')
    expect(backoffBadge).toBeInTheDocument()
  })

  it('should display Rest-pause badge when SetTitle indicates rest-pause', () => {
    const restPauseSets: (WorkoutLogSerieModel &
      Partial<WorkoutLogSerieModelRef>)[] = [
      {
        Id: -1001,
        SetNo: '1',
        Reps: 10,
        Weight: { Lb: 135, Kg: 61.23 },
        IsFinished: false,
        IsNext: true,
        IsWarmups: false,
        SetTitle: 'Rest-pause',
        NbPause: 2,
      },
    ]

    render(
      <ExerciseSetsGrid
        exercise={mockExercise}
        sets={restPauseSets}
        onSetUpdate={vi.fn()}
      />
    )

    const restPauseBadge = screen.getByText('Rest-pause')
    expect(restPauseBadge).toBeInTheDocument()
  })

  it('should display Drop set badge when IsDropSet is true', () => {
    const dropSets: (WorkoutLogSerieModel &
      Partial<WorkoutLogSerieModelRef>)[] = [
      {
        Id: -1001,
        SetNo: '1',
        Reps: 10,
        Weight: { Lb: 135, Kg: 61.23 },
        IsFinished: false,
        IsNext: true,
        IsWarmups: false,
        SetTitle: 'Drop set',
        IsDropSet: true,
      },
      {
        Id: -1002,
        SetNo: '2',
        Reps: 10,
        Weight: { Lb: 115, Kg: 52.16 },
        IsFinished: false,
        IsNext: false,
        IsWarmups: false,
        SetTitle: 'Drop set',
        IsDropSet: true,
      },
    ]

    render(
      <ExerciseSetsGrid
        exercise={mockExercise}
        sets={dropSets}
        onSetUpdate={vi.fn()}
      />
    )

    const dropSetBadges = screen.getAllByText('Drop set')
    expect(dropSetBadges).toHaveLength(2)
  })

  it('should not display badges for normal sets without special SetTitle', () => {
    const normalSets: (WorkoutLogSerieModel &
      Partial<WorkoutLogSerieModelRef>)[] = [
      {
        Id: -1001,
        SetNo: '1',
        Reps: 10,
        Weight: { Lb: 135, Kg: 61.23 },
        IsFinished: false,
        IsNext: true,
        IsWarmups: false,
        SetTitle: 'Working sets:', // Normal title, not a special set type
      },
      {
        Id: -1002,
        SetNo: '2',
        Reps: 10,
        Weight: { Lb: 135, Kg: 61.23 },
        IsFinished: false,
        IsNext: false,
        IsWarmups: false,
        SetTitle: '',
      },
    ]

    render(
      <ExerciseSetsGrid
        exercise={mockExercise}
        sets={normalSets}
        onSetUpdate={vi.fn()}
      />
    )

    // Should not find any set type badges
    const badges = screen.queryAllByRole('button', { name: /set type/i })
    expect(badges).toHaveLength(0)
  })
})
