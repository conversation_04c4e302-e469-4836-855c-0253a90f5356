'use client'

import React from 'react'

interface SetCellHeaderProps {
  unit: 'kg' | 'lbs'
}

export function SetCellHeader({ unit }: SetCellHeaderProps) {
  return (
    <div className="grid grid-cols-[25px_60px_1fr_25px_1fr] gap-0 px-4 py-3 bg-brand-primary text-text-inverse font-bold">
      <div />
      <div className="text-center">SET</div>
      <div className="text-center">REPS</div>
      <div className="text-center">*</div>
      <div className="text-center">{unit.toUpperCase()}</div>
    </div>
  )
}
