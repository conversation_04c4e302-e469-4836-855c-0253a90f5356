/**
 * Validates if an exercise ID is valid for navigation
 * @param exerciseId - The exercise ID to validate
 * @returns true if the ID is a valid positive integer, false otherwise
 */
export function isValidExerciseId(
  exerciseId: number | null | undefined | unknown
): exerciseId is number {
  // Type guard first - must be a number
  if (typeof exerciseId !== 'number') {
    return false
  }

  // Must not be NaN, Infinity, or -Infinity
  if (!Number.isFinite(exerciseId)) {
    return false
  }

  // Must be a positive integer (no decimals, no zero, no negative)
  if (!Number.isInteger(exerciseId) || exerciseId <= 0) {
    return false
  }

  return true
}
