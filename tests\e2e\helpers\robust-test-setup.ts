import { Page, BrowserContext, test } from '@playwright/test'
import { safePageClose, safeContextClose } from './safe-cleanup'

/**
 * Robust test setup that handles browser context lifecycle properly
 * Prevents "Target page, context or browser has been closed" errors
 */
export function setupRobustTestCleanup() {
  test.afterEach(async ({ page, context }) => {
    console.log('🧹 [ROBUST-CLEANUP] Starting cleanup...')

    try {
      // Check if page exists and is accessible
      if (page) {
        try {
          if (!page.isClosed()) {
            console.log(`🌐 [ROBUST-CLEANUP] Context state: ${page.context().pages().length} pages`)
            console.log(`📄 [ROBUST-CLEANUP] Page URL: ${page.url()}`)
          }
        } catch (error) {
          console.log('⚠️ [ROBUST-CLEANUP] Page/context already closed during info gathering')
        }

        // Use safe cleanup helper
        await safePageClose(page)
        console.log('✅ [ROBUST-CLEANUP] Page closed successfully')
      }

      // Additional context cleanup if needed
      if (context) {
        try {
          const pages = context.pages()
          if (pages.length > 0) {
            console.log(`🧹 [ROBUST-CLEANUP] Cleaning up ${pages.length} remaining pages`)
            for (const remainingPage of pages) {
              await safePageClose(remainingPage)
            }
          }
        } catch (error) {
          console.log('⚠️ [ROBUST-CLEANUP] Context already closed during page enumeration')
        }
      }

    } catch (error) {
      console.error('❌ [ROBUST-CLEANUP] Error during cleanup:', error)
      // Don't throw - cleanup errors shouldn't fail tests
    }

    console.log('✅ [ROBUST-CLEANUP] Cleanup completed')
  })
}

/**
 * Enhanced page setup with better error handling
 */
export async function setupPageWithRetry(page: Page, url: string, maxRetries = 3): Promise<void> {
  let lastError: Error | null = null
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔄 [PAGE-SETUP] Attempt ${attempt}/${maxRetries} to navigate to ${url}`)
      
      // Check if page is still valid before navigation
      if (page.isClosed()) {
        throw new Error('Page is closed before navigation')
      }

      await page.goto(url, { 
        waitUntil: 'networkidle',
        timeout: 30000 
      })
      
      console.log(`✅ [PAGE-SETUP] Successfully navigated to ${url}`)
      return
      
    } catch (error) {
      lastError = error as Error
      console.log(`⚠️ [PAGE-SETUP] Attempt ${attempt} failed: ${error}`)
      
      if (attempt < maxRetries) {
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt))
      }
    }
  }
  
  throw new Error(`Failed to setup page after ${maxRetries} attempts. Last error: ${lastError?.message}`)
}

/**
 * Wait for element with better error handling
 */
export async function waitForElementSafely(
  page: Page, 
  selector: string, 
  options: { timeout?: number; visible?: boolean } = {}
) {
  const { timeout = 30000, visible = true } = options
  
  try {
    if (page.isClosed()) {
      throw new Error('Page is closed')
    }
    
    const element = page.locator(selector)
    if (visible) {
      await element.waitFor({ state: 'visible', timeout })
    } else {
      await element.waitFor({ state: 'attached', timeout })
    }
    
    return element
  } catch (error) {
    console.error(`❌ [WAIT-ELEMENT] Failed to find element ${selector}: ${error}`)
    throw error
  }
}

/**
 * Safe click with retry logic
 */
export async function clickSafely(
  page: Page, 
  selector: string, 
  options: { timeout?: number; retries?: number } = {}
) {
  const { timeout = 30000, retries = 3 } = options
  
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      if (page.isClosed()) {
        throw new Error('Page is closed')
      }
      
      const element = await waitForElementSafely(page, selector, { timeout })
      await element.click({ timeout })
      return
      
    } catch (error) {
      console.log(`⚠️ [CLICK-SAFELY] Attempt ${attempt}/${retries} failed for ${selector}: ${error}`)
      
      if (attempt < retries) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      } else {
        throw error
      }
    }
  }
}
