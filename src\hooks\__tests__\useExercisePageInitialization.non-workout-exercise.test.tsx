import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook } from '@testing-library/react'
import { useExercisePageInitialization } from '../useExercisePageInitialization'
import { useWorkout } from '../useWorkout'
import { useRouter } from 'next/navigation'
import type { ExerciseModel } from '@/types'

vi.mock('../useWorkout')
vi.mock('next/navigation')
vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: () => ({
    setCurrentExerciseById: vi.fn(),
    loadingStates: new Map(),
    getCachedExerciseRecommendation: vi.fn(),
  }),
}))
vi.mock('@/utils/debugLog', () => ({
  debugLog: Object.assign(vi.fn(), {
    error: vi.fn(),
  }),
}))

describe('useExercisePageInitialization - Non-workout exercise handling', () => {
  const mockRouter = { replace: vi.fn() }
  const mockStartWorkout = vi.fn()
  const mockLoadRecommendation = vi.fn()
  const mockUpdateExerciseWorkSets = vi.fn()

  const mockExercises: ExerciseModel[] = [
    { Id: 1, Label: 'Bench Press', IsTimeBased: false },
    { Id: 2, Label: 'Squat', IsTimeBased: false },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useRouter).mockReturnValue(mockRouter as any)
    vi.mocked(useWorkout).mockReturnValue({
      todaysWorkout: [
        {
          Id: 1,
          Label: 'Test Workout',
          WorkoutTemplates: [
            {
              Id: 1,
              Label: 'Workout A',
              Exercises: mockExercises,
            },
          ],
        },
      ],
      isLoadingWorkout: false,
      startWorkout: mockStartWorkout,
      exercises: mockExercises,
      workoutSession: { id: 'test-session' },
      loadRecommendation: mockLoadRecommendation,
      updateExerciseWorkSets: mockUpdateExerciseWorkSets,
    } as any)
  })

  it('should not attempt to load sets for exercises not in the workout', () => {
    const nonExistentExerciseId = 841 // Exercise not in mockExercises

    const { result } = renderHook(() =>
      useExercisePageInitialization(nonExistentExerciseId)
    )

    // Should not call loadRecommendation for non-existent exercise
    expect(mockLoadRecommendation).not.toHaveBeenCalled()

    // Should not call updateExerciseWorkSets for non-existent exercise
    expect(mockUpdateExerciseWorkSets).not.toHaveBeenCalled()

    // Should show an error
    expect(result.current.loadingError).toBeTruthy()
    expect(result.current.loadingError?.message).toContain(
      'not found in workout'
    )
  })

  it('should load sets for exercises that exist in the workout', () => {
    const existingExerciseId = 1 // Exercise exists in mockExercises

    const { result } = renderHook(() =>
      useExercisePageInitialization(existingExerciseId)
    )

    // Should call loadRecommendation for existing exercise
    expect(mockLoadRecommendation).toHaveBeenCalledWith(1, 'Bench Press')

    // Should not have an error
    expect(result.current.loadingError).toBeNull()
  })

  it('should handle retry for non-workout exercises gracefully', async () => {
    const nonExistentExerciseId = 2060

    const { result } = renderHook(() =>
      useExercisePageInitialization(nonExistentExerciseId)
    )

    // Initial state should have error
    expect(result.current.loadingError).toBeTruthy()

    // Try to retry
    await result.current.retryInitialization()

    // Should still have error after retry
    expect(result.current.loadingError).toBeTruthy()
    expect(mockLoadRecommendation).not.toHaveBeenCalled()
  })
})
