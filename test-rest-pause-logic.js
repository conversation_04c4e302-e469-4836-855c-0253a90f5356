// Test the rest-pause logic directly
// Run this in Node.js or browser console

const mockRecommendation = {
  ExerciseId: 123,
  Series: 1,
  Reps: 12,
  Weight: { Lb: 135, Kg: 61.2 },
  NbPauses: 3,
  NbRepsPauses: 8,
  RpRest: 15,
  IsNormalSets: false,
  IsPyramid: false,
  IsReversePyramid: false,
  WarmupsCount: 2
};

// Test the logic
const workSetCount = mockRecommendation.Series || 0;
const warmupCount = mockRecommendation.WarmupsCount || 0;

let isRestPause = !mockRecommendation.IsNormalSets && 
                  !mockRecommendation.IsPyramid && 
                  !mockRecommendation.IsReversePyramid &&
                  mockRecommendation.NbPauses > 0;

const totalWorkSets = isRestPause 
  ? workSetCount + (mockRecommendation.NbPauses || 0)
  : workSetCount;

console.log('Test Results:');
console.log('workSetCount:', workSetCount);
console.log('warmupCount:', warmupCount);
console.log('isRestPause:', isRestPause);
console.log('totalWorkSets:', totalWorkSets);
console.log('Expected total sets:', warmupCount + totalWorkSets);

// Expected: 2 warmup + 1 main + 3 rest-pause = 6 total sets
console.log('Expected breakdown:');
console.log('- Warmup sets: 2');
console.log('- Main working set: 1 (12 reps)');
console.log('- Rest-pause sets: 3 (8 reps each)');
console.log('- Total: 6 sets');

// Test set generation logic
const sets = [];
let setNumber = 1;

// Add warmup sets
for (let i = 0; i < warmupCount; i++) {
  sets.push({
    setNumber: setNumber++,
    type: 'warmup',
    reps: 5,
    weight: { Lb: 95, Kg: 43 }
  });
}

// Add work sets
for (let i = 0; i < totalWorkSets; i++) {
  if (isRestPause && i >= workSetCount) {
    // Rest-pause mini-set
    const restPauseIndex = i - workSetCount + 1;
    sets.push({
      setNumber: setNumber++,
      type: 'rest-pause',
      title: restPauseIndex === 1 ? "All right! Now let's try:" : `Rest-pause set ${restPauseIndex}`,
      reps: mockRecommendation.NbRepsPauses,
      weight: mockRecommendation.Weight,
      restTime: mockRecommendation.RpRest
    });
  } else {
    // Main working set
    sets.push({
      setNumber: setNumber++,
      type: 'working',
      title: i === 0 ? '1st work set' : `Set ${i + 1}`,
      reps: mockRecommendation.Reps,
      weight: mockRecommendation.Weight
    });
  }
}

console.log('\nGenerated sets:');
sets.forEach(set => {
  console.log(`Set ${set.setNumber}: ${set.type} - ${set.reps} reps @ ${set.weight.Lb}lbs ${set.title ? '(' + set.title + ')' : ''}`);
});

console.log(`\nTotal sets generated: ${sets.length}`);
