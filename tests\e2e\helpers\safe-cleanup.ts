import { Page, BrowserContext } from '@playwright/test'

/**
 * Safely close a Playwright page with defensive checks
 * Prevents "Target page, context or browser has been closed" errors
 */
export async function safePageClose(page: Page): Promise<void> {
  try {
    // Check if page exists and is not already closed
    if (!page || page.isClosed()) {
      return
    }

    // Check if the context is still valid
    let context: BrowserContext | null = null
    try {
      context = page.context()
      // Try to access context properties to verify it's still valid
      if (context && context.pages) {
        const pages = context.pages()
        // Context is valid if we can get pages
      }
    } catch (contextError) {
      // Context is already closed, page cleanup not needed
      return
    }

    // Attempt to close the page
    await page.close()
  } catch (error) {
    // Ignore errors if page/context/browser is already closed
    if (
      error instanceof Error &&
      (error.message.includes(
        'Target page, context or browser has been closed'
      ) ||
        error.message.includes('Target closed') ||
        error.message.includes('Connection closed') ||
        error.message.includes('Browser has been closed') ||
        error.message.includes('Context has been closed'))
    ) {
      // Expected error when page/context/browser is already closed, ignore
      return
    }
    // Re-throw unexpected errors
    throw error
  }
}

/**
 * Safely close a browser context with defensive checks
 */
export async function safeContextClose(context: BrowserContext): Promise<void> {
  try {
    if (!context) {
      return
    }

    // Check if context is still valid by trying to access its pages
    try {
      const pages = context.pages()
      // If we can get pages, context is still valid
    } catch (contextError) {
      // Context is already closed
      return
    }

    await context.close()
  } catch (error) {
    if (
      error instanceof Error &&
      (error.message.includes('Target closed') ||
        error.message.includes('Connection closed') ||
        error.message.includes('Browser has been closed') ||
        error.message.includes('Context has been closed'))
    ) {
      return
    }
    throw error
  }
}

/**
 * Ensure all browser contexts are properly cleaned up
 */
export async function ensureCleanBrowserState(): Promise<void> {
  // Add a small delay to allow any pending operations to complete
  await new Promise((resolve) => setTimeout(resolve, 100))
}
