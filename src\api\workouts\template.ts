/**
 * Workout Template Modification API
 *
 * Functions for modifying workout templates - adding, removing, and reordering exercises
 */

import { apiClient } from '../client'
import { logger } from '@/utils/logger'
import { getCurrentUserEmail } from '@/lib/auth-utils'
import type { WorkoutTemplateModel, BooleanModel } from '@/types'

/**
 * Add an exercise to a workout template at a specific position
 * @param workoutId - The workout template ID
 * @param exerciseId - The exercise ID to add
 * @param position - The position to insert the exercise (0-based index)
 * @returns Success response
 */
export async function addExerciseToWorkout(
  workoutId: number,
  exerciseId: number,
  position: number
): Promise<BooleanModel> {
  try {
    const username = getCurrentUserEmail()
    if (!username) {
      throw new Error('User not authenticated')
    }

    logger.log(
      `[Template API] Adding exercise ${exerciseId} to workout ${workoutId} at position ${position}`
    )

    const response = await apiClient.post('/api/WorkoutTemplate/AddExercise', {
      WorkoutId: workoutId,
      ExerciseId: exerciseId,
      Position: position,
      Username: username,
    })

    const { data } = response
    return {
      Result: data.Result || false,
      Code: data.Code || 200,
      ErrorMessage: data.ErrorMessage,
    }
  } catch (error) {
    logger.error('[Template API] Failed to add exercise to workout:', error)
    throw error
  }
}

/**
 * Remove an exercise from a workout template
 * @param workoutId - The workout template ID
 * @param exerciseId - The exercise ID to remove
 * @returns Success response
 */
export async function removeExerciseFromWorkout(
  workoutId: number,
  exerciseId: number
): Promise<BooleanModel> {
  try {
    const username = getCurrentUserEmail()
    if (!username) {
      throw new Error('User not authenticated')
    }

    logger.log(
      `[Template API] Removing exercise ${exerciseId} from workout ${workoutId}`
    )

    const response = await apiClient.post(
      '/api/WorkoutTemplate/RemoveExercise',
      {
        WorkoutId: workoutId,
        ExerciseId: exerciseId,
        Username: username,
      }
    )

    const { data } = response
    return {
      Result: data.Result || false,
      Code: data.Code || 200,
      ErrorMessage: data.ErrorMessage,
    }
  } catch (error) {
    logger.error(
      '[Template API] Failed to remove exercise from workout:',
      error
    )
    throw error
  }
}

/**
 * Replace an exercise in a workout template with another exercise
 * This is a compound operation that removes the old exercise and adds the new one in the same position
 * @param workoutId - The workout template ID
 * @param oldExerciseId - The exercise ID to replace
 * @param newExerciseId - The new exercise ID
 * @returns Success response
 */
export async function replaceExerciseInWorkout(
  workoutId: number,
  oldExerciseId: number,
  newExerciseId: number
): Promise<BooleanModel> {
  try {
    const username = getCurrentUserEmail()
    if (!username) {
      throw new Error('User not authenticated')
    }

    logger.log(
      `[Template API] Replacing exercise ${oldExerciseId} with ${newExerciseId} in workout ${workoutId}`
    )

    // First, we need to get the current workout to find the position of the old exercise
    const workoutResponse = await apiClient.get(
      `/api/Workout/GetUserCustomizedCurrentWorkout/${workoutId}`
    )
    const workout: WorkoutTemplateModel =
      workoutResponse.data.Result || workoutResponse.data

    if (!workout.Exercises) {
      throw new Error('Workout has no exercises')
    }

    // Find the position of the old exercise
    const oldExerciseIndex = workout.Exercises.findIndex(
      (ex) => ex.Id === oldExerciseId
    )
    if (oldExerciseIndex === -1) {
      throw new Error(`Exercise ${oldExerciseId} not found in workout`)
    }

    // Use a compound API call if available, otherwise do it in sequence
    try {
      // Try the compound endpoint first (if it exists)
      const response = await apiClient.post(
        '/api/WorkoutTemplate/ReplaceExercise',
        {
          WorkoutId: workoutId,
          OldExerciseId: oldExerciseId,
          NewExerciseId: newExerciseId,
          Position: oldExerciseIndex,
          Username: username,
        }
      )

      const { data } = response
      return {
        Result: data.Result || false,
        Code: data.Code || 200,
        ErrorMessage: data.ErrorMessage,
      }
    } catch (compoundError) {
      // If compound endpoint doesn't exist, fall back to sequential operations
      logger.log(
        '[Template API] Compound replace not available, using sequential operations'
      )

      // Step 1: Remove the old exercise
      const removeResult = await removeExerciseFromWorkout(
        workoutId,
        oldExerciseId
      )
      if (!removeResult.Result) {
        throw new Error(
          `Failed to remove old exercise: ${removeResult.ErrorMessage}`
        )
      }

      // Step 2: Add the new exercise at the same position
      const addResult = await addExerciseToWorkout(
        workoutId,
        newExerciseId,
        oldExerciseIndex
      )
      if (!addResult.Result) {
        // Try to rollback by adding the old exercise back
        try {
          await addExerciseToWorkout(workoutId, oldExerciseId, oldExerciseIndex)
        } catch (rollbackError) {
          logger.error(
            '[Template API] Failed to rollback after failed add:',
            rollbackError
          )
        }
        throw new Error(`Failed to add new exercise: ${addResult.ErrorMessage}`)
      }

      return {
        Result: true,
        Code: 200,
        ErrorMessage: undefined,
      }
    }
  } catch (error) {
    logger.error('[Template API] Failed to replace exercise in workout:', error)
    throw error
  }
}

/**
 * Save a modified workout template
 * @param workout - The modified workout template
 * @returns Success response
 */
export async function saveWorkoutTemplate(
  workout: WorkoutTemplateModel
): Promise<BooleanModel> {
  try {
    const username = getCurrentUserEmail()
    if (!username) {
      throw new Error('User not authenticated')
    }

    logger.log(`[Template API] Saving workout template ${workout.Id}`)

    const response = await apiClient.post('/api/WorkoutTemplate/Save', {
      ...workout,
      Username: username,
    })

    const { data } = response
    return {
      Result: data.Result || false,
      Code: data.Code || 200,
      ErrorMessage: data.ErrorMessage,
    }
  } catch (error) {
    logger.error('[Template API] Failed to save workout template:', error)
    throw error
  }
}
