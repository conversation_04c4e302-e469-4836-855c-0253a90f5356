import { describe, it, expect, vi } from 'vitest'
import { render } from '@testing-library/react'
import { ExerciseSetsGrid } from '../ExerciseSetsGrid'
import type { ExerciseModel, RecommendationModel } from '@/types'

// Mock SetCell to capture the props it receives
const mockSetCell = vi.fn()
vi.mock('../SetCell', () => ({
  SetCell: (props: any) => {
    const { setNo } = props
    mockSetCell(props)
    return <div data-testid={`set-cell-${setNo}`} />
  },
}))

// Mock ExplainerBox
vi.mock('../ExplainerBox', () => ({
  ExplainerBox: () => null,
}))

describe('ExerciseSetsGrid - Recommendation Prop Structure', () => {
  const mockExercise: ExerciseModel = {
    Id: 123,
    Label: 'Bench Press',
    IsBodyweight: false,
    IsSystemExercise: true,
    IsSwapTarget: false,
    IsFinished: false,
    IsUnilateral: false,
    IsTimeBased: false,
    IsEasy: false,
    IsMedium: false,
    IsNextExercise: false,
    IsPlate: false,
    IsWeighted: true,
    IsPyramid: false,
    IsNormalSets: true,
    IsBodypartPriority: false,
    IsFlexibility: false,
    IsOneHanded: false,
    VideoUrl: '',
    LocalVideo: '',
    IsAssisted: false,
  }

  const mockRecommendation: RecommendationModel = {
    ExerciseId: 123, // This is the correct property name
    Series: 3,
    Reps: 10,
    Weight: { Lb: 135, Kg: 61.23 },
    WarmupsCount: 0,
    RpRest: 60,
    NbPauses: 0,
    NbRepsPauses: 0,
    IsNormalSets: false,
    IsDropSet: true,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should pass correct recommendation structure to SetCell with ExerciseId property', () => {
    render(
      <ExerciseSetsGrid
        exercise={mockExercise}
        recommendation={mockRecommendation}
        onSetUpdate={vi.fn()}
        unit="lbs"
      />
    )

    // Find the call that includes the recommendation prop
    const callsWithRecommendation = mockSetCell.mock.calls.filter(
      (call) => call[0].recommendation !== undefined
    )

    expect(callsWithRecommendation.length).toBeGreaterThan(0)

    // Check the recommendation structure
    const recommendationProp = callsWithRecommendation[0][0].recommendation

    // The bug: Currently using recommendation.Id which doesn't exist
    // Should be using recommendation.ExerciseId
    expect(recommendationProp).toBeDefined()
    expect(recommendationProp.ExerciseId).toBe(123) // This should pass after fix
    expect(recommendationProp.Weight).toEqual({ Lb: 135, Kg: 61.23 })
    expect(recommendationProp.Reps).toBe(10)
  })

  it('should pass setType to SetCell based on recommendation', () => {
    render(
      <ExerciseSetsGrid
        exercise={mockExercise}
        recommendation={mockRecommendation}
        onSetUpdate={vi.fn()}
        unit="lbs"
      />
    )

    // Find calls with setType
    const callsWithSetType = mockSetCell.mock.calls.filter(
      (call) => call[0].setType !== undefined
    )

    expect(callsWithSetType.length).toBeGreaterThan(0)

    // With IsDropSet: true, the setType should be 'Drop set'
    expect(callsWithSetType[0][0].setType).toBe('Drop set')
  })

  it('should handle missing recommendation gracefully', () => {
    const { container } = render(
      <ExerciseSetsGrid
        exercise={mockExercise}
        recommendation={undefined}
        onSetUpdate={vi.fn()}
        unit="lbs"
      />
    )

    // When there's no recommendation and no sets, it should show "No sets" message
    const noSetsMessage = container.querySelector('p')
    expect(noSetsMessage?.textContent).toContain('No sets for this exercise')
  })
})
