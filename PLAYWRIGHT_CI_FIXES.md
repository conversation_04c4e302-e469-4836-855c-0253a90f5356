# Playwright CI Fixes for "Target page, context or browser has been closed" Errors

## Problem Summary
Critical E2E tests were passing locally but failing in CI with "Target page, context or browser has been closed" errors, specifically on Mobile Safari Full browser configuration. This indicates browser context/process instability in the CI environment.

## Root Cause Analysis
1. **Resource Exhaustion**: Self-hosted macOS runners may have limited resources
2. **Browser Process Instability**: Safari/WebKit processes becoming zombie or crashing
3. **Race Conditions**: Browser contexts closing before test cleanup
4. **Insufficient Error Handling**: Tests not handling browser closure gracefully

## Implemented Fixes

### 1. CI Workflow Improvements (.github/workflows/ci-optimized.yml)

**Added Process Cleanup Before Playwright Installation:**
```yaml
- name: Cleanup zombie Safari/WebKit processes
  run: |
    echo "Cleaning up zombie processes..."
    pkill -9 safaridriver || true
    pkill -9 WebKitWebContent || true
    pkill -9 WebKitNetworkProcess || true
    pkill -9 WebKitWebProcess || true
    pkill -9 com.apple.WebKit.WebContent || true
    echo "Process cleanup completed"
```

This cleanup is added before all Playwright installations in:
- race-condition-tests job
- e2e-critical job  
- e2e-full job

### 2. Playwright Configuration Improvements (playwright.ci.optimized.config.ts)

**Increased Timeouts and Retries:**
- Test timeout: 60s → 90s
- Retries: 2 → 3
- Browser launch timeout: 120s → 180s

**Added WebKit Stability Arguments:**
```typescript
launchOptions: {
  timeout: 180000,
  args: [
    '--disable-web-security',
    '--disable-features=TranslateUI',
    '--disable-ipc-flooding-protection',
  ],
}
```

### 3. Enhanced Safe Cleanup (tests/e2e/helpers/safe-cleanup.ts)

**Improved safePageClose Function:**
- Added context validity checks before page operations
- Enhanced error message matching for browser/context closure
- Added defensive checks for context.pages() access

**New safeContextClose Function:**
- Handles browser context cleanup with error handling
- Prevents context-related cleanup errors

### 4. Robust Test Setup Helper (tests/e2e/helpers/robust-test-setup.ts)

**New setupRobustTestCleanup Function:**
- Centralized cleanup logic for all test suites
- Enhanced error handling and logging
- Handles multiple page cleanup within context

**New Helper Functions:**
- `setupPageWithRetry`: Navigation with retry logic
- `waitForElementSafely`: Element waiting with error handling  
- `clickSafely`: Click actions with retry mechanism

### 5. Updated Test Files

**Modified Test Files to Use Robust Cleanup:**
- tests/e2e/offline-mode-behavior.spec.ts
- tests/e2e/security.spec.ts
- tests/e2e/workout-components.spec.ts
- tests/e2e/workout-creation-tracking.spec.ts
- tests/e2e/workout-flows.spec.ts

**Changes Made:**
- Replaced individual afterEach cleanup with `setupRobustTestCleanup()`
- Enhanced beforeEach login flows with retry logic
- Added proper error handling and logging

## Expected Results

### Immediate Improvements:
1. **Reduced Browser Crashes**: Process cleanup prevents zombie processes
2. **Better Error Recovery**: Enhanced retry logic handles transient failures
3. **Improved Stability**: Longer timeouts accommodate slower CI environment
4. **Graceful Degradation**: Tests handle browser closure without failing

### Monitoring Points:
1. **CI Success Rate**: Should see significant reduction in "Target closed" errors
2. **Test Duration**: May increase slightly due to longer timeouts and retries
3. **Resource Usage**: Monitor memory/CPU usage in CI logs
4. **Process Count**: Check for zombie process reduction in failure logs

## Next Steps

1. **Deploy Changes**: Commit and push changes to trigger CI run
2. **Monitor Results**: Watch for reduction in browser closure errors
3. **Fine-tune Timeouts**: Adjust timeouts based on CI performance
4. **Expand Coverage**: Apply robust cleanup to remaining test files if needed

## Rollback Plan

If issues persist:
1. Revert playwright.ci.optimized.config.ts timeout changes
2. Remove process cleanup steps from CI workflow
3. Fall back to original cleanup mechanisms
4. Consider switching from WebKit to Chromium for CI testing

## Additional Considerations

- **Resource Monitoring**: CI workflow already includes resource reporting
- **Parallel Execution**: Already limited to 1 worker for stability
- **Browser Selection**: May need to evaluate WebKit vs Chromium for CI
- **Self-hosted Runner**: Consider runner resource allocation if issues persist
