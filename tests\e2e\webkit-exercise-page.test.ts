import { test, expect } from '@playwright/test'

test.describe('WebKit Exercise Page Tests', () => {
  test('should load exercise page and show Save Set button', async ({
    page,
    browserName,
  }) => {
    // This test verifies the exercise page loads properly on WebKit

    // Navigate to exercise page directly (simplified test)
    await page.goto('/workout/exercise/1')

    // Add WebKit-specific wait time
    if (browserName === 'webkit') {
      await page.waitForTimeout(2000)
    }

    // Wait for any loading states to complete
    await page.waitForFunction(
      () => {
        const bodyText = document.body.textContent || ''
        return !bodyText.includes('Loading') && !bodyText.includes('loading')
      },
      { timeout: 20000 }
    )

    // Check multiple selectors to find the save button
    const saveButtonSelectors = [
      '[data-testid="floating-save-button"]',
      'button:has-text("Save Set")',
      'text=Save Set',
    ]

    let buttonFound = false

    // Check each selector sequentially
    const firstSelector = await page.locator(saveButtonSelectors[0]).first()
    if (await firstSelector.isVisible({ timeout: 5000 }).catch(() => false)) {
      buttonFound = true
    } else {
      const secondSelector = await page.locator(saveButtonSelectors[1]).first()
      if (
        await secondSelector.isVisible({ timeout: 5000 }).catch(() => false)
      ) {
        buttonFound = true
      } else {
        const thirdSelector = await page.locator(saveButtonSelectors[2]).first()
        if (
          await thirdSelector.isVisible({ timeout: 5000 }).catch(() => false)
        ) {
          buttonFound = true
        }
      }
    }

    expect(buttonFound).toBeTruthy()
  })
})
