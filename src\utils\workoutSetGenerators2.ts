import type { ExerciseModel, RecommendationModel } from '@/types'
import type { WorkoutLogSerieModelRef } from './createWorkoutSets'
import { createBaseWorkSet } from './workoutSetGenerators'

/**
 * Generates back-off sets (lighter sets after main set)
 */
export function generateBackOffSets(
  exercise: ExerciseModel,
  recommendation: RecommendationModel,
  existingSetsCount: number,
  isKg: boolean
): WorkoutLogSerieModelRef[] {
  const backOffSets: WorkoutLogSerieModelRef[] = []

  for (let j = 0; j < recommendation.Series; j++) {
    const rec = createBaseWorkSet(
      exercise,
      recommendation,
      j,
      existingSetsCount,
      isKg
    )

    if (j === 0) {
      // Main set - use recommendation weight/reps
      rec.SetTitle = ''
    } else {
      // Back-off sets (20% lighter)
      rec.Weight = recommendation.BackOffSetWeight || {
        Kg: recommendation.Weight.Kg * 0.8,
        Lb: recommendation.Weight.Lb * 0.8,
      }
      rec.Reps = recommendation.Reps + 2
      rec.SetTitle = 'Back-off set:'
      rec.IsBackOffSet = true
    }

    backOffSets.push(rec)
  }

  return backOffSets
}

/**
 * Generates rest-pause set
 */
export function generateRestPauseSet(
  exercise: ExerciseModel,
  recommendation: RecommendationModel,
  existingSetsCount: number,
  isKg: boolean
): WorkoutLogSerieModelRef[] {
  const rec = createBaseWorkSet(
    exercise,
    recommendation,
    0,
    existingSetsCount,
    isKg
  )

  rec.SetTitle = 'Rest-pause'
  rec.NbPause = recommendation.NbPauses
  rec.IsFirstWorkSet = true
  rec.IsLastSet = true

  return [rec]
}

/**
 * Generates normal work sets
 */
export function generateNormalSets(
  exercise: ExerciseModel,
  recommendation: RecommendationModel,
  existingSetsCount: number,
  isKg: boolean
): WorkoutLogSerieModelRef[] {
  const normalSets: WorkoutLogSerieModelRef[] = []

  for (let j = 0; j < recommendation.Series; j++) {
    const rec = createBaseWorkSet(
      exercise,
      recommendation,
      j,
      existingSetsCount,
      isKg
    )

    rec.SetTitle = j === 0 && recommendation.Series > 1 ? 'Working sets:' : ''
    rec.IsNormalset = true

    normalSets.push(rec)
  }

  return normalSets
}
