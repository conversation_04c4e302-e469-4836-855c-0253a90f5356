import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import { ShareButton } from '../ShareButton'

describe('ShareButton - Fallback Behavior', () => {
  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()
    // Remove navigator.share to test fallback
    Object.defineProperty(navigator, 'share', {
      value: undefined,
      writable: true,
      configurable: true,
    })
  })

  it('should still render button when Web Share API is not supported', () => {
    render(<ShareButton />)

    const button = screen.getByRole('button', { name: /share/i })
    expect(button).toBeInTheDocument()
  })

  it('should copy to clipboard when Web Share API is not supported', async () => {
    const mockWriteText = vi.fn().mockResolvedValue(undefined)
    Object.defineProperty(navigator, 'clipboard', {
      value: { writeText: mockWriteText },
      writable: true,
      configurable: true,
    })

    render(<ShareButton />)

    const button = screen.getByRole('button', { name: /share/i })
    fireEvent.click(button)

    await waitFor(() => {
      expect(mockWriteText).toHaveBeenCalledWith(
        'Workout Complete!\n\nI just completed my workout with Dr. Muscle X!'
      )
    })
  })

  it('should copy custom share data to clipboard', async () => {
    const mockWriteText = vi.fn().mockResolvedValue(undefined)
    Object.defineProperty(navigator, 'clipboard', {
      value: { writeText: mockWriteText },
      writable: true,
      configurable: true,
    })

    const customShareData = {
      title: 'Amazing Workout!',
      text: '10 exercises, 50 sets, 10,000 lbs lifted!',
    }

    render(<ShareButton shareData={customShareData} />)

    const button = screen.getByRole('button', { name: /share/i })
    fireEvent.click(button)

    await waitFor(() => {
      expect(mockWriteText).toHaveBeenCalledWith(
        'Amazing Workout!\n\n10 exercises, 50 sets, 10,000 lbs lifted!'
      )
    })
  })

  it('should show "Copied!" feedback when copying to clipboard', async () => {
    const mockWriteText = vi.fn().mockResolvedValue(undefined)
    Object.defineProperty(navigator, 'clipboard', {
      value: { writeText: mockWriteText },
      writable: true,
      configurable: true,
    })

    render(<ShareButton showLabel />)

    const button = screen.getByRole('button', { name: /share/i })
    fireEvent.click(button)

    // Should show "Copied!" temporarily
    await waitFor(() => {
      expect(button).toHaveTextContent('Copied!')
    })

    // Should revert back to "Share" after timeout
    await waitFor(
      () => {
        expect(button).toHaveTextContent('Share')
      },
      { timeout: 3000 }
    )
  })

  it('should handle clipboard write failure gracefully', async () => {
    const mockWriteText = vi
      .fn()
      .mockRejectedValue(new Error('Clipboard failed'))
    Object.defineProperty(navigator, 'clipboard', {
      value: { writeText: mockWriteText },
      writable: true,
      configurable: true,
    })

    render(<ShareButton />)

    const button = screen.getByRole('button', { name: /share/i })

    // Should not throw error
    expect(() => fireEvent.click(button)).not.toThrow()
  })

  it('should not show feedback when showLabel is false', async () => {
    const mockWriteText = vi.fn().mockResolvedValue(undefined)
    Object.defineProperty(navigator, 'clipboard', {
      value: { writeText: mockWriteText },
      writable: true,
      configurable: true,
    })

    render(<ShareButton showLabel={false} />)

    const button = screen.getByRole('button', { name: /share/i })
    const originalContent = button.innerHTML

    fireEvent.click(button)

    // Content should not change
    await waitFor(() => {
      expect(button.innerHTML).toBe(originalContent)
    })
  })
})
