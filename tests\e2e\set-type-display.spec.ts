import { test, expect } from '@playwright/test'

test.describe('Set Type Display', () => {
  test.beforeEach(async ({ page }) => {
    // Login
    await page.goto('/login')
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.fill('input[name="password"]', 'Dr123456')
    await page.click('button[type="submit"]')

    // Wait for navigation to program page
    await page.waitForURL('/program')

    // Navigate to workout
    await page.click('text=Start Workout')
    await page.waitForURL('/workout')

    // Click first exercise
    await page.click('[data-testid="exercise-card-0"]')
    await page.waitForSelector('[data-testid="exercise-client"]')
  })

  test('should display set type badges for special sets', async ({ page }) => {
    // Mock recommendations with different set types
    await page.route('**/GetRecommendationForExercise', async (route) => {
      const json = {
        Series: 3,
        Reps: 10,
        Weight: { Lb: 135, Kg: 61.23 },
        IsNormalSets: false,
        NbPauses: 2,
        NbRepsPauses: 3,
        RpRest: 15,
        IsDropSet: false,
        IsBackOffSet: false,
        IsPyramid: false,
        IsReversePyramid: false,
        WarmupsCount: 0,
      }
      await route.fulfill({ json })
    })

    // Reload to get mocked recommendations
    await page.reload()
    await page.waitForSelector('[data-testid="exercise-client"]')

    // Check for Rest-pause badge
    const badge = page.locator('button:has-text("Rest-pause")')
    await expect(badge).toBeVisible()

    // Badge should have proper styling
    await expect(badge).toHaveClass(/bg-brand-primary\/10/)
    await expect(badge).toHaveClass(/text-brand-primary/)

    // Badge should have minimum touch target
    const box = await badge.boundingBox()
    expect(box?.height).toBeGreaterThanOrEqual(44)
  })

  test('should show explainer when badge is tapped', async ({ page }) => {
    // Mock recommendations with drop set
    await page.route('**/GetRecommendationForExercise', async (route) => {
      const json = {
        Series: 3,
        Reps: 10,
        Weight: { Lb: 135, Kg: 61.23 },
        IsNormalSets: true,
        NbPauses: 0,
        IsDropSet: true,
        IsBackOffSet: false,
        IsPyramid: false,
        IsReversePyramid: false,
        WarmupsCount: 0,
      }
      await route.fulfill({ json })
    })

    await page.reload()
    await page.waitForSelector('[data-testid="exercise-client"]')

    // Find and click drop set badge
    const badge = page.locator('button:has-text("Drop set")')
    await badge.click()

    // Check explainer appears
    const explainer = page.locator('text=/Immediately reduce the weight/i')
    await expect(explainer).toBeVisible()

    // Check it has proper region role
    const explainerRegion = page.locator(
      '[role="region"][aria-label="Drop set set explanation"]'
    )
    await expect(explainerRegion).toBeVisible()

    // Click badge again to hide
    await badge.click()
    await expect(explainer).not.toBeVisible()
  })

  test('should not show badge for normal sets', async ({ page }) => {
    // Mock recommendations with normal sets
    await page.route('**/GetRecommendationForExercise', async (route) => {
      const json = {
        Series: 3,
        Reps: 10,
        Weight: { Lb: 135, Kg: 61.23 },
        IsNormalSets: true,
        NbPauses: 0,
        IsDropSet: false,
        IsBackOffSet: false,
        IsPyramid: false,
        IsReversePyramid: false,
        WarmupsCount: 0,
      }
      await route.fulfill({ json })
    })

    await page.reload()
    await page.waitForSelector('[data-testid="exercise-client"]')

    // Should not have any set type badges
    const badges = page.locator(
      'button:has-text("Rest-pause"), button:has-text("Drop set"), button:has-text("Pyramid")'
    )
    await expect(badges).toHaveCount(0)
  })

  test('should display correct badges for pyramid sets', async ({ page }) => {
    // Mock pyramid set
    await page.route('**/GetRecommendationForExercise', async (route) => {
      const json = {
        Series: 4,
        Reps: 12,
        Weight: { Lb: 100, Kg: 45.36 },
        IsNormalSets: true,
        NbPauses: 0,
        IsDropSet: false,
        IsBackOffSet: false,
        IsPyramid: true,
        IsReversePyramid: false,
        WarmupsCount: 0,
      }
      await route.fulfill({ json })
    })

    await page.reload()
    await page.waitForSelector('[data-testid="exercise-client"]')

    // Check for Pyramid badge
    const badge = page.locator('button:has-text("Pyramid")')
    await expect(badge).toBeVisible()

    // Click to show explainer
    await badge.click()

    // Check pyramid explanation
    const explainer = page.locator(
      'text=/Weight increases while reps decrease/i'
    )
    await expect(explainer).toBeVisible()
  })

  test('should display abbreviated text for reverse pyramid', async ({
    page,
  }) => {
    // Mock reverse pyramid set
    await page.route('**/GetRecommendationForExercise', async (route) => {
      const json = {
        Series: 4,
        Reps: 6,
        Weight: { Lb: 225, Kg: 102.06 },
        IsNormalSets: true,
        NbPauses: 0,
        IsDropSet: false,
        IsBackOffSet: false,
        IsPyramid: false,
        IsReversePyramid: true,
        WarmupsCount: 0,
      }
      await route.fulfill({ json })
    })

    await page.reload()
    await page.waitForSelector('[data-testid="exercise-client"]')

    // Check for abbreviated badge text
    const badge = page.locator('button:has-text("Rev pyramid")')
    await expect(badge).toBeVisible()

    // Click to show full explainer
    await badge.click()

    // Check full explanation mentions "Reverse pyramid"
    const explainer = page.locator('text=/Reverse pyramid sets/i')
    await expect(explainer).toBeVisible()
  })
})
