# Development Patterns & Gotchas

This document captures important patterns, common issues, and their solutions based on our development experience. Always consult this before implementing new features.

## Common Issues & Solutions

### Navigation & Routing

#### Invalid Exercise ID Navigation

**Issue**: Users see "Exercise Not Available" error when accessing invalid exercise URLs (e.g., `/workout/exercise/0`, `/workout/exercise/abc`).
**Root Cause**: Dynamic route accepts any string ID, but `parseInt()` can return `NaN` or invalid numbers.
**Solution**: Add server-side validation at the route level before rendering client component:

```typescript
// In src/app/workout/exercise/[id]/page.tsx
export default async function ExercisePage({ params }: ExercisePageProps) {
  const { id } = await params
  const exerciseId = parseInt(id)

  // Validate exercise ID at the route level
  if (!isValidExerciseId(exerciseId)) {
    redirect('/workout')
  }

  return <ExercisePageClient exerciseId={exerciseId} />
}
```

**Key Points**:

- Use `isValidExerciseId()` utility to check for positive integers
- Server-side redirect prevents error boundary from triggering
- Maintains existing validation for exercises not in current workout

### Race Conditions

#### Workout Start Race Condition

**Issue**: Users see blank screen when starting workout due to exercises not loading before navigation.
**Root Cause**: React closure captures stale `exercises` array, navigation happens before `startWorkout` completes.
**Solution**: Return `firstExerciseId` from `startWorkout` function and use fresh data:

```typescript
// BAD: Uses potentially stale exercises array
const firstExercise = exercises[0]
navigate(`/workout/exercise/${firstExercise.Id}`)

// GOOD: Uses fresh data from store action
const { firstExerciseId } = await startWorkout()
navigate(`/workout/exercise/${firstExerciseId}`)
```

#### Exercise Recommendations Loading

**Issue**: Exercise page shows no recommendations when navigating too quickly.
**Pattern**: Always wait for async operations to complete before navigation.

```typescript
// Ensure recommendations are loaded
await loadRecommendation(exerciseId)
// Then navigate
```

### Component State Management

#### Active Set Display Updates

**Issue**: Arrow buttons don't update displayed values for active set.
**Root Cause**: `generateAllSets()` wasn't using current `setData` for active set.
**Solution**: Incorporate `setData` values when generating sets:

```typescript
// For active set, use current setData values
if (isActiveSet) {
  set.Reps = setData.reps
  set.Weight = convertWeight(setData.weight, userUnit)
}
```

#### Navigation Title Persistence

**Issue**: Exercise name persists in navigation header when leaving exercise page.
**Solution**: Clear dynamic title when unmounting:

```typescript
useEffect(() => {
  return () => setTitle(null) // Clear on unmount
}, [])
```

### Testing Patterns

#### WebKit/Safari Stability

**Issue**: Tests fail intermittently on WebKit due to timing issues.
**Solutions**:

1. Add explicit waits for UI elements: `await page.waitForSelector('.save-button', { state: 'visible' })`
2. Use more specific selectors: `button:has-text("Save set")` instead of generic classes
3. Add retry logic for flaky operations
4. Check element visibility before interaction

#### Test Data Isolation

**Pattern**: Always use unique test data to avoid conflicts:

```typescript
const uniqueEmail = `test-${Date.now()}@example.com`
```

#### Mocking API Responses

**Pattern**: Mock at the network level for E2E tests, at the module level for unit tests:

```typescript
// E2E: Use Playwright's route handler
await page.route('**/api/exercise/*', (route) =>
  route.fulfill({ body: mockData })
)

// Unit: Use vitest mocks
vi.mock('@/services/api', () => ({ getExercise: vi.fn() }))
```

## UI/UX Patterns

### Touch Targets

**Requirement**: Minimum 44px touch targets for mobile.
**Implementation**: Use padding, not just size:

```css
/* BAD: Only 32px clickable area */
.button {
  width: 32px;
  height: 32px;
}

/* GOOD: 44px clickable area with padding */
.button {
  width: 32px;
  height: 32px;
  padding: 6px; /* Total 44px */
}
```

### Grid Layout for Sets

**Pattern**: Display all sets at once in a grid format:

- Headers: SET | REPS | \* | LBS/KG
- Show 0-6 warmup sets and 2-6 work sets based on API
- Active set shows arrow buttons for adjustment
- Completed sets show check mark, skipped show X

### Save Button Behavior

**Pattern**: Consolidate save functionality within the grid:

- Save button appears under active set only
- Shows loading state during save
- Disable during save to prevent double-submission

## State Management Patterns

### Zustand Store Best Practices

**Pattern**: Return fresh data from actions to avoid closure issues:

```typescript
// Store action
startWorkout: async () => {
  const workout = await api.startWorkout()
  set({ workout, exercises: workout.exercises })
  return { firstExerciseId: workout.exercises[0].Id }
}
```

### Component Splitting for Maintainability

**Rule**: Max 200 lines per component. Split large components:

- Extract state logic into custom hooks
- Extract sub-components for distinct UI sections
- Extract action handlers into separate modules

Example: `WorkoutOverview.tsx` split into:

- `WorkoutOverview.tsx` (main component)
- `WorkoutOverviewStates.tsx` (state sections)
- `WorkoutOverviewActions.tsx` (action buttons)

## API Integration Patterns

### Error Handling

**Pattern**: Handle expected errors gracefully:

```typescript
// Reduce console noise for expected 404s
if (error.response?.status === 404) {
  console.info('No sets found for exercise')
  return { sets: [] }
}
```

### Unit Conversion

**Always**: Convert weights based on user preference:

```typescript
const displayWeight = convertWeight(
  weight,
  userInfo?.MassUnit === 'kg' ? 'kg' : 'lbs'
)
```

### Type Safety

**Rule**: Never use `any` types. Common type issues:

- Exercise IDs are numbers, not strings
- Recommendation can be null, handle accordingly
- Weight can be 0, don't prevent this

## Performance Patterns

### Bundle Size

**Constraint**: < 150KB initial JavaScript
**Check**: `npm run analyze` before committing
**Common issues**:

- Importing entire libraries instead of specific functions
- Not using dynamic imports for large components

### Mobile-First Development

**Always test on mobile viewport (320-430px)**:

- Use mobile gestures (swipe navigation)
- Optimize for mobile bandwidth
- Test touch interactions
- Verify performance on mid-range devices

## Don't Repeat These Mistakes

1. **Never skip tests**: Run `npm run typecheck`, `npm run lint`, `npm run test` before every commit
2. **Never use --no-verify**: Fix the issues, don't skip checks
3. **Never assume library availability**: Check package.json first
4. **Never update git config**: Use existing settings
5. **Never create files unless necessary**: Prefer editing existing files
6. **Never ignore TypeScript errors**: Fix them properly
7. **Never forget mobile testing**: All UI changes must be tested on mobile viewport
8. **Never commit without testing arrow functionality**: If touching sets UI, test increment/decrement
9. **Never navigate without ensuring data is loaded**: Prevent race conditions
10. **Never ignore user preferences**: Always respect kg/lbs settings
11. **Never trust route parameters**: Always validate dynamic route IDs at the server level

## Quick Reference Commands

```bash
# Before ANY commit
npm run typecheck
npm run lint
npm run test
npm run build

# Check bundle size
npm run analyze

# Test specific areas
npm run test:unit
npm run test:components
npm run test:changed
```

### Set ID Generation and React Key Conflicts

**Issue**: React key conflicts and console warnings when rendering sets due to duplicate IDs.
**Root Cause**: Warmup and work sets used overlapping ID ranges, causing React to lose track of components.
**Solution**: Use distinct negative ID ranges for different set types:

```typescript
// Generate unique IDs to prevent React key conflicts
const warmupSets = sets.warmup.map((set, index) => ({
  ...set,
  Id: -(1000 + index), // Warmup sets: -1000, -1001, etc.
}))

const workSets = sets.work.map((set, index) => ({
  ...set,
  Id: -(2000 + index), // Work sets: -2000, -2001, etc.
}))
```

**Key Points**:

- Always use unique negative IDs for client-generated sets
- Reserve distinct ID ranges for different set types
- This prevents React key warnings and component state confusion

### Set Type Badge Display

**Issue**: Individual set type badges not showing for specialized sets (Rest-pause, Pyramid, Drop, etc.).
**Root Cause**: Set type detection relied only on exercise-level properties, not individual set flags.
**Solution**: Check both SetTitle and individual set flags:

```typescript
export function getSetTypeFromSet(set: any): SetType {
  // Check individual set flags first
  if (set.IsRestPause) return 'Rest-pause'
  if (set.IsPyramid) return 'Pyramid'
  if (set.IsDrop) return 'Drop'
  if (set.IsBackOff) return 'Back-off'
  if (set.IsReversePyramid) return 'Reverse pyramid'

  // Fallback to SetTitle for other types
  return getSetTypeFromTitle(set.SetTitle)
}
```

### Arrow Functionality Weight Decrements

**Issue**: Weight decrement arrows not working properly - reducing by 1 instead of proper increments.
**Root Cause**: Decrement logic wasn't using recommendation-based increments.
**Solution**: Apply same increment logic for both directions:

```typescript
const handleWeightDecrement = () => {
  const currentWeight = parseFloat(setData.weight) || 0
  const increment = recommendation?.WeightIncrement || 5
  const newWeight = Math.max(0, currentWeight - increment)
  updateSetData({ weight: newWeight.toString() })
}
```

**Key Points**:

- Always use `recommendation.WeightIncrement` for both increment and decrement
- Ensure minimum weight of 0 (never negative)
- Convert to string for form input compatibility

### Set Numbering Display

**Issue**: Confusing set numbering where warmup and work sets had overlapping numbers.
**Solution**: Clear visual distinction in numbering:

```typescript
// Display warmup sets as 'W' and work sets starting from 1
const getSetDisplayNumber = (set: any, setIndex: number, isWarmup: boolean) => {
  return isWarmup ? 'W' : (setIndex + 1).toString()
}
```

## Recent Architectural Decisions

1. **Grid Layout for Sets**: Replaced list view with grid showing all sets at once
2. **Inline Editing**: Direct editing in grid cells instead of modal
3. **Arrow Controls**: Quick increment/decrement for active set only
4. **Consolidated Save**: Single save button under active set
5. **Unique Set IDs**: Use distinct negative ranges to prevent React key conflicts
6. **Individual Set Type Badges**: Check individual set flags for specialized set types
7. **Status File Gitignored**: Use this patterns file and git history for context instead
