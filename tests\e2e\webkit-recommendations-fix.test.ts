import { test, expect } from '@playwright/test'

// Simplified test to debug WebKit issue
test.describe('WebKit Recommendations Fix', () => {
  test('simplified recommendations flow', async ({ page, browserName }) => {
    test.setTimeout(120000)

    // Skip non-WebKit browsers for this debug test
    if (browserName !== 'webkit') {
      test.skip()
      return
    }

    // Starting simplified WebKit test...

    // Direct navigation to avoid complex flows
    await page.goto('/login')

    // Simple login
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'Dr123456')
    await page.click('button[type="submit"]')

    // Wait for navigation
    await page.waitForURL('/program', { timeout: 30000 }).catch(() => {
      return page.goto('/program')
    })

    // Find and click workout button
    const workoutButton = page.getByRole('button', {
      name: /open workout|continue/i,
    })
    await workoutButton.waitFor({ timeout: 20000 })
    await workoutButton.click()

    // Navigate to workout
    await page.waitForURL(/\/workout/, { timeout: 20000 })

    // Click first exercise
    const exerciseCard = page.locator('[data-testid="exercise-card"]').first()
    await exerciseCard.waitFor({ timeout: 20000 })
    await exerciseCard.click()

    // Wait for exercise page
    await page.waitForURL(/\/workout\/exercise\/\d+/, { timeout: 20000 })

    // Simple check - just verify we're on the page
    const url = page.url()
    expect(url).toMatch(/\/workout\/exercise\/\d+/)

    // Test completed successfully!
  })
})
