import React from 'react'
import { render, screen } from '@testing-library/react'
import { NavigationWrapper } from '../NavigationWrapper'
import { usePathname } from 'next/navigation'
import { vi } from 'vitest'

// Mock next/navigation
vi.mock('next/navigation', () => ({
  usePathname: vi.fn(),
  useRouter: () => ({
    push: vi.fn(),
    back: vi.fn(),
  }),
}))

// Mock auth hook
vi.mock('@/hooks/useAuth', () => ({
  useAuth: () => ({ user: null }),
}))

// Mock workout store
vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: () => vi.fn(),
}))

// Mock navigation config
vi.mock('@/config/navigationConfig', () => ({
  getNavigationConfig: vi.fn((pathname: string) => {
    if (pathname === '/workout') {
      return { title: 'Workout', showBackButton: false }
    }
    if (pathname.startsWith('/workout/exercise/')) {
      return { title: 'Exercise', showBackButton: true }
    }
    return { title: 'Default', showBackButton: false }
  }),
}))

describe('NavigationWrapper - Title Cleanup', () => {
  const mockUsePathname = usePathname as ReturnType<typeof vi.fn>

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should clear dynamic title when navigating away from exercise page', async () => {
    // Start on exercise page
    mockUsePathname.mockReturnValue('/workout/exercise/123')

    const { rerender } = render(
      <NavigationWrapper>
        <div>Test Content</div>
      </NavigationWrapper>
    )

    // Verify exercise page shows (will be set by SetScreenWithGrid)
    expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent(
      'Exercise'
    )

    // Navigate to workout page
    mockUsePathname.mockReturnValue('/workout')
    rerender(
      <NavigationWrapper>
        <div>Test Content</div>
      </NavigationWrapper>
    )

    // Title should show static workout title, not persisted exercise name
    expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent(
      'Workout'
    )
  })

  it('should maintain dynamic title when navigating between exercise pages', () => {
    // Start on first exercise page
    mockUsePathname.mockReturnValue('/workout/exercise/123')

    const { rerender } = render(
      <NavigationWrapper>
        <div>Test Content</div>
      </NavigationWrapper>
    )

    // Navigate to another exercise page
    mockUsePathname.mockReturnValue('/workout/exercise/456')
    rerender(
      <NavigationWrapper>
        <div>Test Content</div>
      </NavigationWrapper>
    )

    // Should still show exercise title (will be updated by new exercise component)
    expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent(
      'Exercise'
    )
  })

  it('should not affect pages that never set dynamic titles', () => {
    // Start on a page without dynamic title
    mockUsePathname.mockReturnValue('/settings')

    render(
      <NavigationWrapper>
        <div>Test Content</div>
      </NavigationWrapper>
    )

    // Should show default title
    expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent(
      'Default'
    )
  })
})
