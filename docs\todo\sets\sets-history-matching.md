How the Mobile App Handles HistorySet

For Warmup Sets:
Direct index matching from HistorySet array (warmup 0 → HistorySet[0], etc.)

For Work Sets:
First work set: Uses SetCoachTipTitle method which displays data from FirstWorkSetReps/FirstWorkSetWeight
Subsequent work sets:
Filters HistorySet to get only work sets (IsWarmups == false)
Uses index matching within the filtered work sets array
Work set 0 → workcount[0], Work set 1 → workcount[1], etc.
The Complete Matching Logic:
Warmups: HistorySet[i] where i is the warmup index
Work sets:
Filter: HistorySet.Where(x => x.IsWarmups == false)
Match: filteredWorkSets[j] where j is the work set index

Edge Cases:
If there are fewer historical sets than current sets, the extra current sets show empty "Last time"
The matching is purely position-based within each set type (warmup/work)

So for your example scenario:

Warmup 1: Shows HistorySet[0] = "Last time: 5 × 45 lbs"
Work Set 1: Shows filtered work sets[0] = "Last time: 10 × 135 lbs"
Work Set 2: Shows filtered work sets[1] = "Last time: 8 × 135 lbs"
Work Set 3: Shows filtered work sets[2] = "Last time: 6 × 135 lbs"
Work Set 4: Shows nothing (no matching historical set)
Thank you for catching my error! The mobile app does indeed show historical data for all sets, not just the first work set.
