import { describe, it, expect } from 'vitest'
import {
  roundToNearestIncrement,
  formatWeight,
  truncateDecimal,
  getWeightSingal,
  getWeightDouble,
  createMultiUnityWeight,
  replaceWithDot,
} from '../weightHelpers'

describe('weightHelpers', () => {
  describe('roundToNearestIncrement', () => {
    it('should round to nearest increment', () => {
      expect(roundToNearestIncrement(67.3, 2.5)).toBe(67.5)
      expect(roundToNearestIncrement(68.8, 2.5)).toBe(70)
      expect(roundToNearestIncrement(66.1, 2.5)).toBe(65)
    })

    it('should handle zero increment by using 1', () => {
      expect(roundToNearestIncrement(67.3, 0)).toBe(67)
    })

    it('should respect minimum value', () => {
      expect(roundToNearestIncrement(2.3, 2.5, 5)).toBe(5)
    })

    it('should respect maximum value', () => {
      expect(roundToNearestIncrement(102.3, 2.5, undefined, 100)).toBe(100)
    })

    it('should handle both min and max constraints', () => {
      expect(roundToNearestIncrement(2.3, 2.5, 5, 100)).toBe(5)
      expect(roundToNearestIncrement(102.3, 2.5, 5, 100)).toBe(100)
      expect(roundToNearestIncrement(67.3, 2.5, 5, 100)).toBe(67.5)
    })
  })

  describe('formatWeight', () => {
    it('should format weight in kg', () => {
      const weight = { Kg: 67.5, Lb: 148.81 }
      expect(formatWeight(weight, true)).toBe('67.5 kg')
    })

    it('should format weight in lbs', () => {
      const weight = { Kg: 67.5, Lb: 148.81 }
      expect(formatWeight(weight, false)).toBe('148.81 lbs')
    })

    it('should handle null weight', () => {
      expect(formatWeight(null, true)).toBe('')
      expect(formatWeight(null, false)).toBe('')
    })

    it('should round to 2 decimal places', () => {
      const weight = { Kg: 67.123456, Lb: 148.123456 }
      expect(formatWeight(weight, true)).toBe('67.12 kg')
      expect(formatWeight(weight, false)).toBe('148.12 lbs')
    })
  })

  describe('truncateDecimal', () => {
    it('should truncate to specified precision', () => {
      expect(truncateDecimal(67.123456, 2)).toBe(67.12)
      expect(truncateDecimal(67.999999, 2)).toBe(67.99)
      expect(truncateDecimal(67.123456, 0)).toBe(67)
    })

    it('should handle negative numbers', () => {
      expect(truncateDecimal(-67.123456, 2)).toBe(-67.12)
    })

    it('should handle zero precision', () => {
      expect(truncateDecimal(67.999999, 0)).toBe(67)
    })
  })

  describe('getWeightSingal', () => {
    it('should return formatted weight signal in kg', () => {
      const weight = { Kg: 67.5, Lb: 148.81 }
      expect(getWeightSingal(weight, true)).toBe('67.5')
    })

    it('should return formatted weight signal in lbs', () => {
      const weight = { Kg: 67.5, Lb: 148.81 }
      expect(getWeightSingal(weight, false)).toBe('148.81')
    })

    it('should round to 2 decimal places', () => {
      const weight = { Kg: 67.123456, Lb: 148.123456 }
      expect(getWeightSingal(weight, true)).toBe('67.12')
      expect(getWeightSingal(weight, false)).toBe('148.12')
    })
  })

  describe('getWeightDouble', () => {
    it('should return formatted weight double in kg', () => {
      const weight = { Kg: 67.5, Lb: 148.81 }
      expect(getWeightDouble(weight, true)).toBe('67.5')
    })

    it('should return formatted weight double in lbs', () => {
      const weight = { Kg: 67.5, Lb: 148.81 }
      expect(getWeightDouble(weight, false)).toBe('148.81')
    })
  })

  describe('createMultiUnityWeight', () => {
    it('should create weight from kg value', () => {
      const weight = createMultiUnityWeight(60, 'kg')
      expect(weight.Kg).toBe(60)
      expect(weight.Lb).toBeCloseTo(132.28, 2) // 60 * 2.20462
    })

    it('should create weight from lb value', () => {
      const weight = createMultiUnityWeight(132, 'lb')
      expect(weight.Lb).toBe(132)
      expect(weight.Kg).toBeCloseTo(59.87, 2) // 132 / 2.20462
    })
  })

  describe('replaceWithDot', () => {
    it('should replace comma with dot', () => {
      expect(replaceWithDot('67,5')).toBe('67.5')
      expect(replaceWithDot('123,456')).toBe('123.456')
    })

    it('should handle strings without comma', () => {
      expect(replaceWithDot('67.5')).toBe('67.5')
      expect(replaceWithDot('123')).toBe('123')
    })

    it('should handle empty string', () => {
      expect(replaceWithDot('')).toBe('')
    })

    it('should handle multiple commas', () => {
      expect(replaceWithDot('1,234,567')).toBe('1.234.567')
    })
  })
})
