import { describe, it, expect } from 'vitest'
import type { RecommendationModel } from '../recommendation'

describe('RecommendationModel', () => {
  it('should include IsNewStarted field to check if exercise is initialized', () => {
    const recommendation: RecommendationModel = {
      ExerciseId: 123,
      Series: 0,
      Reps: 0,
      Weight: { Lb: 0, Kg: 0 },
      IsNewStarted: true, // New field to indicate uninitialized exercise
      WarmupsCount: 0,
      WarmUpsList: [],
      HistorySet: [],
      ReferenceSetHistory: null,
      RecommendationInKG: { Lb: 0, Kg: 0 },
      NbPauses: 0,
      NbRepsPauses: 0,
      RpRest: 0,
      SetStyle: 'Normal',
      BackOffSetWeight: { Lb: 0, Kg: 0 },
      WarmUpReps1: 0,
      WarmUpWeightSet1: { Lb: 0, Kg: 0 },
      WarmUpReps2: 0,
      WarmUpWeightSet2: { Lb: 0, Kg: 0 },
      LastLogDate: '',
      SetNo: '1',
      Trainer: '',
      WeightRecommandation: 0,
      IsDeload: false,
      Challenge: '',
      BodyWeight: 0,
      IsBodyweight: false,
      IsTimeBased: false,
      IsPyramid: false,
      IsReversePyramid: false,
      IsFinished: false,
      IsMedium: false,
      RIR: 0,
      FirstWorkSetWeight: { Lb: 0, Kg: 0 },
      TrainingMax: 0,
      IsFromProgramLog: false,
      IsMaxChallenge: false,
      IsPlate: false,
      IsNormalSets: false,
      TotalVolume: { Lb: 0, Kg: 0 },
      TimeCommited: '',
      Timer: 0,
      ExerciseName: '',
      VideUrl: '',
      MaxWeight: 0,
      OneRMText: '',
      TargetIntensityPercentage: 0,
      TargetRpe: 0,
      Plates: [],
      PlatesInKg: [],
      EquipmentId: 0,
      IsEasy: false,
      IsSystemExercise: false,
      FailureCount: 0,
      LastTimeDialogShown: '',
      EquivalentPlates: '',
    }

    // The type should compile with IsNewStarted field
    expect(recommendation.IsNewStarted).toBe(true)
  })

  it('should detect uninitialized exercise when Series is 0 and IsNewStarted is true', () => {
    const uninitializedRecommendation: RecommendationModel = {
      ExerciseId: 123,
      Series: 0, // No sets available
      IsNewStarted: true, // Exercise not initialized
      Reps: 0,
      Weight: { Lb: 0, Kg: 0 },
      WarmupsCount: 0,
      WarmUpsList: [],
      HistorySet: [],
      ReferenceSetHistory: null,
      RecommendationInKG: { Lb: 0, Kg: 0 },
      NbPauses: 0,
      NbRepsPauses: 0,
      RpRest: 0,
      SetStyle: 'Normal',
      BackOffSetWeight: { Lb: 0, Kg: 0 },
      WarmUpReps1: 0,
      WarmUpWeightSet1: { Lb: 0, Kg: 0 },
      WarmUpReps2: 0,
      WarmUpWeightSet2: { Lb: 0, Kg: 0 },
      LastLogDate: '',
      SetNo: '1',
      Trainer: '',
      WeightRecommandation: 0,
      IsDeload: false,
      Challenge: '',
      BodyWeight: 0,
      IsBodyweight: false,
      IsTimeBased: false,
      IsPyramid: false,
      IsReversePyramid: false,
      IsFinished: false,
      IsMedium: false,
      RIR: 0,
      FirstWorkSetWeight: { Lb: 0, Kg: 0 },
      TrainingMax: 0,
      IsFromProgramLog: false,
      IsMaxChallenge: false,
      IsPlate: false,
      IsNormalSets: false,
      TotalVolume: { Lb: 0, Kg: 0 },
      TimeCommited: '',
      Timer: 0,
      ExerciseName: '',
      VideUrl: '',
      MaxWeight: 0,
      OneRMText: '',
      TargetIntensityPercentage: 0,
      TargetRpe: 0,
      Plates: [],
      PlatesInKg: [],
      EquipmentId: 0,
      IsEasy: false,
      IsSystemExercise: false,
      FailureCount: 0,
      LastTimeDialogShown: '',
      EquivalentPlates: '',
    }

    // Helper function to check if exercise needs initialization
    const needsInitialization = (
      recommendation: RecommendationModel
    ): boolean => {
      return (
        recommendation.Series === 0 &&
        (recommendation.IsNewStarted === true ||
          recommendation.IsNewStarted === undefined)
      )
    }

    expect(needsInitialization(uninitializedRecommendation)).toBe(true)
  })

  it('should detect initialized exercise when Series > 0', () => {
    const initializedRecommendation: RecommendationModel = {
      ExerciseId: 123,
      Series: 4, // Has sets available
      IsNewStarted: false, // Exercise is initialized
      Reps: 8,
      Weight: { Lb: 100, Kg: 45.36 },
      WarmupsCount: 2,
      WarmUpsList: [],
      HistorySet: [],
      ReferenceSetHistory: null,
      RecommendationInKG: { Lb: 100, Kg: 45.36 },
      NbPauses: 0,
      NbRepsPauses: 0,
      RpRest: 0,
      SetStyle: 'Normal',
      BackOffSetWeight: { Lb: 0, Kg: 0 },
      WarmUpReps1: 5,
      WarmUpWeightSet1: { Lb: 50, Kg: 22.68 },
      WarmUpReps2: 3,
      WarmUpWeightSet2: { Lb: 75, Kg: 34.02 },
      LastLogDate: '',
      SetNo: '1',
      Trainer: '',
      WeightRecommandation: 100,
      IsDeload: false,
      Challenge: '',
      BodyWeight: 0,
      IsBodyweight: false,
      IsTimeBased: false,
      IsPyramid: false,
      IsReversePyramid: false,
      IsFinished: false,
      IsMedium: false,
      RIR: 2,
      FirstWorkSetWeight: { Lb: 100, Kg: 45.36 },
      TrainingMax: 0,
      IsFromProgramLog: false,
      IsMaxChallenge: false,
      IsPlate: false,
      IsNormalSets: true,
      TotalVolume: { Lb: 3200, Kg: 1451.52 },
      TimeCommited: '',
      Timer: 0,
      ExerciseName: 'Bench Press',
      VideUrl: '',
      MaxWeight: 100,
      OneRMText: '110 lbs',
      TargetIntensityPercentage: 0,
      TargetRpe: 0,
      Plates: [],
      PlatesInKg: [],
      EquipmentId: 0,
      IsEasy: false,
      IsSystemExercise: false,
      FailureCount: 0,
      LastTimeDialogShown: '',
      EquivalentPlates: '',
    }

    // Helper function to check if exercise needs initialization
    const needsInitialization = (
      recommendation: RecommendationModel
    ): boolean => {
      return (
        recommendation.Series === 0 &&
        (recommendation.IsNewStarted === true ||
          recommendation.IsNewStarted === undefined)
      )
    }

    expect(needsInitialization(initializedRecommendation)).toBe(false)
  })
})
