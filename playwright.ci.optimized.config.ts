// eslint-disable-next-line import/no-extraneous-dependencies
import { defineConfig, devices } from '@playwright/test'
import { webkitProjectConfig } from './tests/e2e/webkit-config'

/**
 * Optimized CI-specific Playwright configuration
 * with enhanced WebKit stability and memory management
 */
export default defineConfig({
  testDir: './tests/e2e',
  /* Disable parallel execution to prevent browser context conflicts */
  fullyParallel: false,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: true,
  /* Increased retries for WebKit browser stability issues */
  retries: 5,
  /* Use single worker on CI to prevent resource exhaustion on self-hosted runners */
  workers: 1,
  /* Support for sharding tests across multiple machines */
  /* Usage: --shard=1/4 --shard=2/4 etc. */
  /* Reporter configuration for CI */
  reporter: [
    ['html', { outputFolder: 'playwright-report' }],
    ['junit', { outputFile: 'test-results/junit.xml' }],
    ['list'],
    ['json', { outputFile: 'test-results/results.json' }],
  ],
  /* Increased timeout for CI stability and browser launch issues */
  timeout: 180000, // 3 minutes per test
  /* Increased expect timeout for slow CI environments */
  expect: {
    timeout: 45000, // 45 seconds for assertions
  },
  /* Shared settings for all the projects below */
  use: {
    /* Base URL for local testing in CI */
    baseURL: 'http://localhost:3000',
    /* Collect trace when retrying the failed test */
    trace: 'on-first-retry',
    /* Screenshot on failure */
    screenshot: 'only-on-failure',
    /* Video on failure */
    video: 'retain-on-failure',
    /* Increased timeout for each action */
    actionTimeout: 30000,
    /* Increased navigation timeout */
    navigationTimeout: 60000,
    /* Browser launch options for CI stability */
    launchOptions: {
      timeout: 300000, // 5 minutes for browser launch
      // Additional WebKit stability options
      args: [
        '--disable-web-security',
        '--disable-features=TranslateUI',
        '--disable-ipc-flooding-protection',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-background-timer-throttling',
        '--disable-background-networking',
        '--disable-features=VizDisplayCompositor',
        '--disable-dev-shm-usage',
        '--no-sandbox',
        '--disable-setuid-sandbox',
      ],
      // Force single process for WebKit stability
      chromiumSandbox: false,
      // Slow down interactions for better stability
      slowMo: 1000,
    },
    /* Context options for stability */
    contextOptions: {
      // Reduce memory usage
      viewport: { width: 390, height: 844 },
      // Disable unnecessary features
      javaScriptEnabled: true,
      bypassCSP: true,
      ignoreHTTPSErrors: true,
      // Reduce resource usage
      reducedMotion: 'reduce',
      // Disable service workers which can cause issues
      serviceWorkers: 'block',
      // Set explicit locale
      locale: 'en-US',
      // Disable permissions that might cause prompts
      permissions: [],
    },
    /* Global test setup */
    extraHTTPHeaders: {
      'X-Test-Context': 'playwright-ci',
      'Cache-Control': 'no-cache',
    },
  },

  /* Global setup and teardown */
  globalSetup: require.resolve('./tests/e2e/global-setup.ts'),
  globalTeardown: require.resolve('./tests/e2e/global-teardown.ts'),

  /* Configure projects for critical mobile paths */
  projects: [
    /* Mobile Safari - Primary target for critical tests */
    {
      ...webkitProjectConfig,
      name: 'Mobile Safari Critical',
      testMatch: /.*@critical.*\.spec\.ts$/,
      use: {
        ...webkitProjectConfig.use,
        // Enhanced WebKit stability settings
        launchOptions: {
          ...webkitProjectConfig.use.launchOptions,
          timeout: 300000,
          slowMo: 1500, // Even slower for critical tests
          args: [], // WebKit doesn't support Chrome args
          env: {
            ...process.env,
            WEBKIT_DISABLE_COMPOSITING: '1',
            WEBKIT_FORCE_COMPOSITING_MODE: '0',
          },
        },
        contextOptions: {
          ...webkitProjectConfig.use.contextOptions,
          // Additional stability measures
          strictSelectors: false,
        },
        // Increased timeouts for WebKit
        actionTimeout: 45000,
        navigationTimeout: 90000,
      },
      retries: 5,
      workers: 1,
    },
    /* Mobile Chrome - Run for critical tests only as fallback */
    {
      name: 'Mobile Chrome Critical',
      use: {
        ...devices['Pixel 5'],
        viewport: { width: 393, height: 851 },
        hasTouch: true,
        isMobile: true,
        // Chrome-specific options for stability
        launchOptions: {
          timeout: 180000,
          args: [
            '--disable-dev-shm-usage',
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
          ],
          slowMo: 500,
        },
        contextOptions: {
          viewport: { width: 393, height: 851 },
          reducedMotion: 'reduce',
          serviceWorkers: 'block',
        },
        actionTimeout: 30000,
        navigationTimeout: 60000,
      },
      testMatch: /.*@critical.*\.spec\.ts$/,
      retries: 3,
      workers: 1,
    },
    /* Mobile Safari - All tests (non-critical) */
    {
      ...webkitProjectConfig,
      name: 'Mobile Safari Full',
      testIgnore: /.*@critical.*\.spec\.ts$/,
      use: {
        ...webkitProjectConfig.use,
        // Standard WebKit settings for non-critical tests
        launchOptions: {
          ...webkitProjectConfig.use.launchOptions,
          slowMo: 1000,
        },
      },
      retries: 3,
      workers: 1,
    },
  ],

  /* Run local dev server before tests */
  webServer: {
    command: 'npm run start',
    port: 3000,
    reuseExistingServer: !process.env.CI,
    timeout: 180000, // 3 minutes for server startup
    env: {
      NODE_OPTIONS: '--max_old_space_size=8192',
    },
  },
})
