import { useState } from 'react'
import { useRouter } from 'next/navigation'
import type {
  WorkoutTemplateGroupModel,
  ExerciseWorkSetsModel,
  WorkoutSession,
  RecommendationModel,
  SetModel,
} from '@/types'
import { debugLog } from '@/utils/debugLog'
import { isValidExerciseId } from '@/utils/exerciseValidation'

interface UseWorkoutActionsProps {
  todaysWorkout: WorkoutTemplateGroupModel[] | null
  startWorkout: (
    workout: WorkoutTemplateGroupModel[]
  ) => Promise<{ success: boolean; firstExerciseId?: number }>
  exercises: ExerciseWorkSetsModel[]
  workoutSession: WorkoutSession | null
  loadExerciseRecommendation: (
    exerciseId: number
  ) => Promise<RecommendationModel | null>
  updateExerciseWorkSets: (exerciseId: number, sets: SetModel[]) => void
  finishWorkout: () => Promise<boolean>
}

export function useWorkoutActions({
  todaysWorkout,
  startWorkout,
  exercises,
  workoutSession,
  loadExerciseRecommendation,
  updateExerciseWorkSets,
  finishWorkout,
}: UseWorkoutActionsProps) {
  const router = useRouter()
  const [isStartingWorkout, setIsStartingWorkout] = useState(false)

  const handleStartWorkout = async () => {
    if (todaysWorkout && !isStartingWorkout) {
      try {
        setIsStartingWorkout(true)
        // Start workout and wait for preload to complete
        const result = await startWorkout(todaysWorkout)

        if (result.success && result.firstExerciseId) {
          // Validate exercise ID before navigation
          if (isValidExerciseId(result.firstExerciseId)) {
            // Navigate using the first exercise ID from the workout that was just started
            router.push(`/workout/exercise/${result.firstExerciseId}`)
          } else {
            debugLog.error('Invalid first exercise ID:', result.firstExerciseId)
            router.push('/workout')
          }
        }
      } catch (error) {
        debugLog.error('Failed to start workout:', error)
        // Handle error gracefully - could show a toast notification
      } finally {
        setIsStartingWorkout(false)
      }
    }
  }

  const handleFinishWorkout = async () => {
    try {
      await finishWorkout()
      router.push('/workout/complete')
    } catch (error) {
      // Handle error silently
    }
  }

  const handleExerciseClick = async (exerciseId: number) => {
    try {
      // Validate exercise ID before proceeding
      if (!isValidExerciseId(exerciseId)) {
        debugLog.error('Invalid exercise ID clicked:', exerciseId)
        router.push('/workout')
        return
      }

      debugLog.log(`Exercise ${exerciseId} clicked, starting navigation...`)

      // Start workout if not already started
      if (todaysWorkout && !workoutSession) {
        debugLog.log('Starting workout before navigation...')
        const result = await startWorkout(todaysWorkout)
        if (!result.success) {
          debugLog.error(
            'Failed to start workout, attempting navigation anyway'
          )
          // Don't return - still try to navigate
        } else {
          debugLog.log('Workout started successfully')
        }
      }

      // Pre-load the exercise recommendation before navigation
      const exercise = exercises?.find((ex) => ex.Id === exerciseId)
      if (exercise && (!exercise.sets || exercise.sets.length === 0)) {
        // Show loading state
        updateExerciseWorkSets(exerciseId, [])

        try {
          // Try to load recommendation before navigation
          await loadExerciseRecommendation(exerciseId)
          debugLog.log(`Pre-loaded recommendation for exercise ${exerciseId}`)
        } catch (error) {
          // Continue with navigation even if recommendation fails
          // The exercise page will handle the retry
          // Only log in development
          debugLog.warn(
            'Failed to pre-load recommendation, continuing to exercise page:',
            error
          )
        }
      }

      // Navigate to exercise page with timeout protection
      debugLog.log(`Navigating to exercise ${exerciseId}...`)

      // Final validation before navigation
      if (!isValidExerciseId(exerciseId)) {
        debugLog.error(
          'Exercise ID became invalid before navigation:',
          exerciseId
        )
        router.push('/workout')
        return
      }

      // Use a timeout to ensure navigation doesn't hang
      const navigationPromise = router.push(`/workout/exercise/${exerciseId}`)
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Navigation timeout')), 5000)
      })

      await Promise.race([navigationPromise, timeoutPromise])
      debugLog.log(`Navigation to exercise ${exerciseId} completed`)
    } catch (error) {
      debugLog.error('Error handling exercise click:', error)

      // Fallback: try direct navigation without pre-loading
      try {
        if (isValidExerciseId(exerciseId)) {
          debugLog.log('Attempting fallback navigation...')
          router.push(`/workout/exercise/${exerciseId}`)
        } else {
          debugLog.error('Invalid exercise ID in fallback:', exerciseId)
          router.push('/workout')
        }
      } catch (fallbackError) {
        debugLog.error('Fallback navigation also failed:', fallbackError)
        // Last resort: reload the page to the exercise if valid
        if (isValidExerciseId(exerciseId)) {
          window.location.href = `/workout/exercise/${exerciseId}`
        } else {
          window.location.href = '/workout'
        }
      }
    }
  }

  const handleRetryExercise = (exerciseId: number) => {
    // Retry loading sets for a specific exercise
    updateExerciseWorkSets(exerciseId, [])
  }

  const hasCompletedSets =
    workoutSession?.exercises?.some(
      (exercise) => exercise.sets && exercise.sets.length > 0
    ) || false

  const getButtonLabel = () => {
    if (workoutSession && hasCompletedSets) return 'Finish and save workout'
    if (workoutSession) return 'Continue Workout'
    return 'Start Workout'
  }

  const getButtonAriaLabel = () => {
    if (workoutSession && hasCompletedSets) return 'Finish and save workout'
    if (workoutSession) return 'Continue your current workout'
    return 'Start a new workout session'
  }

  return {
    isStartingWorkout,
    handleStartWorkout,
    handleFinishWorkout,
    handleExerciseClick,
    handleRetryExercise,
    hasCompletedSets,
    getButtonLabel,
    getButtonAriaLabel,
  }
}
