import { test, expect } from '@playwright/test'

// Test to verify WebKit browser stability improvements
test.describe('WebKit Browser Stability Tests', () => {
  test('should successfully create and close WebKit browser context', async ({
    browser,
  }) => {
    // Test browser is alive
    expect(browser.isConnected()).toBeTruthy()

    // Create context with stability options
    const context = await browser.newContext({
      viewport: { width: 390, height: 844 },
      userAgent:
        'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15',
    })

    // Verify context creation
    expect(context).toBeDefined()

    // Create page
    const page = await context.newPage()
    expect(page).toBeDefined()

    // Navigate to simple page
    await page.goto(
      'data:text/html,<html><body>WebKit Stability Test</body></html>'
    )

    // Verify page loaded
    const body = await page.textContent('body')
    expect(body).toBe('WebKit Stability Test')

    // Clean close
    await page.close()
    await context.close()

    // Verify browser still connected after context close
    expect(browser.isConnected()).toBeTruthy()
  })

  test('should handle rapid page creation and closure', async ({ browser }) => {
    const context = await browser.newContext()

    // Create and close pages rapidly
    for (let i = 0; i < 5; i++) {
      // eslint-disable-next-line no-await-in-loop
      const page = await context.newPage()
      // eslint-disable-next-line no-await-in-loop
      await page.goto(`data:text/html,<html><body>Test ${i}</body></html>`)
      // eslint-disable-next-line no-await-in-loop
      await page.close()
    }

    // Context should still be valid
    const finalPage = await context.newPage()
    await finalPage.goto('data:text/html,<html><body>Final</body></html>')
    expect(await finalPage.textContent('body')).toBe('Final')

    await finalPage.close()
    await context.close()
  })
})
