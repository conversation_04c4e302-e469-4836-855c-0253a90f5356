import { useState, useCallback } from 'react'
import type { WorkoutLogSerieModelRef } from '@/types/api/WorkoutLogSerieModelRef'
import type { ExerciseModel, RecommendationModel } from '@/types'
import {
  completeSet,
  uncompleteSet,
  getCurrentSetIndex,
  getNextSet,
  canFinishExercise,
  finishExercise,
  addSet,
  removeSet,
  updateSetData,
  getSetStatistics,
} from '@/utils/setStateManager'
import { updateOneRM } from '@/utils/oneRmCalculator'
import {
  validateWeightInput,
  createMultiUnityWeight,
} from '@/utils/weightHelpers'

interface UseExerciseSetManagerProps {
  exercise: ExerciseModel
  recommendation?: RecommendationModel
  initialSets: WorkoutLogSerieModelRef[]
  isKg?: boolean
  userBodyWeight?: number
  onSetUpdate?: (sets: WorkoutLogSerieModelRef[]) => void
  onExerciseComplete?: () => void
  onOneRMUpdate?: (data: unknown) => void
}

/**
 * Comprehensive exercise set management hook
 * Integrates all MAUI functionality for set state management
 */
export function useExerciseSetManager({
  exercise,
  recommendation,
  initialSets,
  isKg = false,
  userBodyWeight = 80,
  onSetUpdate,
  onExerciseComplete,
  onOneRMUpdate,
}: UseExerciseSetManagerProps) {
  const [sets, setSets] = useState<WorkoutLogSerieModelRef[]>(initialSets)

  /**
   * Update sets and notify parent
   */
  const updateSets = useCallback(
    (newSets: WorkoutLogSerieModelRef[]) => {
      setSets(newSets)
      onSetUpdate?.(newSets)
    },
    [onSetUpdate]
  )

  /**
   * Handle set completion toggle
   */
  const handleSetComplete = useCallback(
    (setIndex: number) => {
      const currentSet = sets[setIndex]
      if (!currentSet) return

      const newSets = currentSet.IsFinished
        ? uncompleteSet(sets, setIndex)
        : completeSet(sets, setIndex)

      updateSets(newSets)

      // Check if exercise can be completed
      if (canFinishExercise(newSets)) {
        // Auto-complete exercise if all work sets are done
        // This matches MAUI behavior
      }
    },
    [sets, updateSets]
  )

  /**
   * Handle weight change with validation and 1RM update
   */
  const handleWeightChange = useCallback(
    (setIndex: number, weightInput: string) => {
      const currentSet = sets[setIndex]
      if (!currentSet) return

      const validation = validateWeightInput(
        weightInput,
        // eslint-disable-next-line no-nested-ternary
        recommendation?.Min
          ? isKg
            ? recommendation.Min.Kg
            : recommendation.Min.Lb
          : undefined,
        // eslint-disable-next-line no-nested-ternary
        recommendation?.Max
          ? isKg
            ? recommendation.Max.Kg
            : recommendation.Max.Lb
          : undefined
      )

      if (!validation.isValid) {
        console.warn('Invalid weight input:', validation.error)
        return
      }

      const newWeight = createMultiUnityWeight(
        validation.value,
        isKg ? 'kg' : 'lb'
      )
      const newSets = updateSetData(sets, setIndex, { Weight: newWeight })
      updateSets(newSets)

      // Trigger 1RM update for first work sets
      if (
        currentSet.IsFirstWorkSet &&
        !currentSet.IsBackOffSet &&
        !currentSet.IsWarmups
      ) {
        const oneRMData = updateOneRM(
          validation.value,
          currentSet.Reps || 0,
          exercise!,
          recommendation!,
          isKg,
          userBodyWeight
        )
        onOneRMUpdate?.(oneRMData)
      }
    },
    [
      sets,
      updateSets,
      exercise,
      recommendation,
      isKg,
      userBodyWeight,
      onOneRMUpdate,
    ]
  )

  /**
   * Handle reps change with validation and 1RM update
   */
  const handleRepsChange = useCallback(
    (setIndex: number, repsInput: string) => {
      const currentSet = sets[setIndex]
      if (!currentSet) return

      const reps = parseInt(repsInput, 10)
      if (Number.isNaN(reps) || reps < 1 || reps > 100) {
        console.warn('Invalid reps input:', repsInput)
        return
      }

      const newSets = updateSetData(sets, setIndex, { Reps: reps })
      updateSets(newSets)

      // Trigger 1RM update for first work sets
      if (
        currentSet.IsFirstWorkSet &&
        !currentSet.IsBackOffSet &&
        !currentSet.IsWarmups
      ) {
        const weightValue = isKg
          ? currentSet.Weight?.Kg || 0
          : currentSet.Weight?.Lb || 0
        const oneRMData = updateOneRM(
          weightValue,
          reps,
          exercise!,
          recommendation!,
          isKg,
          userBodyWeight
        )
        onOneRMUpdate?.(oneRMData)
      }
    },
    [
      sets,
      updateSets,
      exercise,
      recommendation,
      isKg,
      userBodyWeight,
      onOneRMUpdate,
    ]
  )

  /**
   * Handle adding a new set
   */
  const handleAddSet = useCallback(() => {
    const newSets = addSet(sets, {})
    updateSets(newSets)
  }, [sets, updateSets])

  /**
   * Handle removing a set
   */
  const handleRemoveSet = useCallback(
    (setIndex: number) => {
      const newSets = removeSet(sets, setIndex)
      updateSets(newSets)
    },
    [sets, updateSets]
  )

  /**
   * Handle exercise completion
   */
  const handleExerciseComplete = useCallback(() => {
    const newSets = finishExercise(sets)
    updateSets(newSets)
    onExerciseComplete?.()
  }, [sets, updateSets, onExerciseComplete])

  /**
   * Get current exercise statistics
   */
  const statistics = getSetStatistics(sets)

  /**
   * Get next set to perform
   */
  const nextSet = getNextSet(sets)

  /**
   * Get current set index
   */
  const currentSetIndex = getCurrentSetIndex(sets)

  return {
    // State
    sets,
    statistics,
    nextSet,
    currentSetIndex,

    // Actions
    handleSetComplete,
    handleWeightChange,
    handleRepsChange,
    handleAddSet,
    handleRemoveSet,
    handleExerciseComplete,

    // Computed properties
    canFinish: statistics.canFinish,
    allCompleted: statistics.allCompleted,
    allWorkSetsCompleted: statistics.allWorkSetsCompleted,
    completionPercentage: statistics.completionPercentage,
    workSetsCompletionPercentage: statistics.workSetsCompletionPercentage,

    // Utilities
    updateSets,
  }
}
