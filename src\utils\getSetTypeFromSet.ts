import type { WorkoutLogSerieModel } from '@/types'
import type { WorkoutLogSerieModelRef } from '@/types/api/WorkoutLogSerieModelRef'
import type { SetType } from './setTypeUtils'

/**
 * Determines the set type from an individual set's properties
 * Uses SetTitle and other set properties to determine the correct type
 */
export function getSetTypeFromSet(
  set: WorkoutLogSerieModel & Partial<WorkoutLogSerieModelRef>
): SetType {
  // Check SetTitle first (most reliable indicator)
  const setTitle = (set as WorkoutLogSerieModelRef).SetTitle?.toLowerCase()

  if (setTitle?.includes('rest-pause')) {
    return 'Rest-pause'
  }

  if (setTitle?.includes('reverse pyramid')) {
    return 'Reverse pyramid'
  }

  if (setTitle?.includes('pyramid')) {
    return 'Pyramid'
  }

  if (setTitle?.includes('back-off') || setTitle?.includes('backoff')) {
    return 'Back-off'
  }

  if (setTitle?.includes('drop')) {
    return 'Drop set'
  }

  // Check set properties as fallback
  const setRef = set as WorkoutLogSerieModelRef

  if (setRef.IsBackOffSet) {
    return 'Back-off'
  }

  if (setRef.IsDropSet) {
    return 'Drop set'
  }

  // Check for rest-pause based on NbPause
  if (setRef.NbPause && setRef.NbPause > 0) {
    return 'Rest-pause'
  }

  return 'Normal'
}
