'use client'

import React from 'react'
import type { SetType } from '@/utils/setTypeUtils'

interface SetTypeBadgeProps {
  setType: SetType
  onClick?: () => void
  variant?: 'default' | 'compact'
}

const setTypeAbbreviations: Record<SetType, string> = {
  Normal: 'Normal',
  'Rest-pause': 'Rest-pause',
  'Back-off': 'Back-off',
  'Drop set': 'Drop set',
  Pyramid: 'Pyramid',
  'Reverse pyramid': 'Rev pyramid',
}

export function SetTypeBadge({
  setType,
  onClick,
  variant = 'default',
}: SetTypeBadgeProps) {
  const displayText = setTypeAbbreviations[setType] || setType

  const baseClasses =
    'inline-flex items-center justify-center text-xs font-medium text-brand-primary bg-brand-primary/10 rounded-full hover:bg-brand-primary/20 transition-colors'
  const variantClasses =
    variant === 'compact'
      ? 'px-2 py-0.5 min-h-[32px] mt-1'
      : 'px-3 py-1 min-h-[44px]'

  return (
    <button
      onClick={onClick}
      className={`${baseClasses} ${variantClasses}`}
      aria-label={`${setType} set type - tap for more info`}
    >
      {displayText}
    </button>
  )
}
