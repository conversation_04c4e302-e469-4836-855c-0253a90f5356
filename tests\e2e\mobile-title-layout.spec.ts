import { test, expect } from '@playwright/test'

test.describe('Mobile Title Layout', () => {
  test.beforeEach(async ({ page }) => {
    // Set mobile viewport (iPhone 12 size)
    await page.setViewportSize({ width: 390, height: 844 })
  })

  test('Dr. Muscle X title should display properly on mobile homepage', async ({
    page,
  }) => {
    await page.goto('/')

    // Wait for the title to be visible
    const title = page.locator('h1:has-text("Dr. Muscle X")')
    await expect(title).toBeVisible()

    // Check that whitespace-nowrap class is applied
    const hasNoWrapClass = await title.evaluate((el) => {
      return el.classList.contains('whitespace-nowrap')
    })
    expect(hasNoWrapClass).toBe(true)

    // Verify the text content
    const textContent = await title.textContent()
    expect(textContent).toBe('Dr. Muscle X')

    // Check that title uses responsive text sizing
    const classes = await title.getAttribute('class')
    expect(classes).toContain('text-2xl')
    expect(classes).toContain('md:text-4xl')
  })
})
