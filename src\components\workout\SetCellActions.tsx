'use client'

import React from 'react'

interface SetCellActionsProps {
  isLastSet: boolean
  isFinished: boolean
  isExerciseFinished: boolean
  onFinishExercise?: () => void
  onAddSet?: () => void
}

export function SetCellActions({
  isLastSet,
  isFinished,
  isExerciseFinished,
  onFinishExercise,
  onAddSet,
}: SetCellActionsProps) {
  return (
    <>
      {/* All sets done message */}
      {isLastSet && isFinished && !isExerciseFinished && (
        <div className="mt-4 bg-success/10 rounded-theme p-4">
          <p className="text-center text-success font-medium italic">
            All sets done—congrats!
          </p>
        </div>
      )}

      {/* Finish Exercise Button */}
      {isLastSet && isFinished && onFinishExercise && (
        <div className="mt-4">
          <button
            onClick={onFinishExercise}
            className="w-full bg-success text-text-inverse font-bold py-4 rounded-theme text-lg hover:bg-success/90 transition-colors min-h-[66px]"
            aria-label="Finish this exercise and move to next"
          >
            Finish exercise
          </button>
        </div>
      )}

      {/* Add Set Button */}
      {isLastSet && !isExerciseFinished && onAddSet && (
        <div className="mt-4">
          <button
            onClick={onAddSet}
            className="w-full border-2 border-accent text-text-primary bg-transparent font-medium py-4 rounded-theme hover:bg-accent/10 transition-colors min-h-[60px]"
          >
            Add set
          </button>
        </div>
      )}
    </>
  )
}
