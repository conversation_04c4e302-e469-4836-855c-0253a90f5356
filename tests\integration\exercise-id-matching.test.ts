import { describe, it, expect } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useWorkoutStore } from '@/stores/workoutStore'

describe('Exercise ID Matching', () => {
  beforeEach(() => {
    const { setState } = useWorkoutStore
    setState({
      exercises: [
        { Id: 123, Label: 'Exercise 1' },
        { Id: '456', Label: 'Exercise 2' }, // String ID
        { Id: 789, Label: 'Exercise 3' },
      ],
      currentExerciseIndex: 0,
    })
  })

  it('should find exercise by numeric ID', () => {
    const { result } = renderHook(() => useWorkoutStore())

    act(() => {
      result.current.setCurrentExerciseById(123)
    })

    expect(result.current.currentExerciseIndex).toBe(0)
  })

  it('should find exercise by string ID converted to number', () => {
    const { result } = renderHook(() => useWorkoutStore())

    act(() => {
      result.current.setCurrentExerciseById(456)
    })

    expect(result.current.currentExerciseIndex).toBe(1)
  })

  it('should handle string exercise ID in data', () => {
    const { result } = renderHook(() => useWorkoutStore())

    // This simulates what happens when exerciseId comes from URL params
    const exerciseIdFromUrl = '456'

    act(() => {
      result.current.setCurrentExerciseById(parseInt(exerciseIdFromUrl))
    })

    expect(result.current.currentExerciseIndex).toBe(1)
  })

  it('should not change index if exercise not found', () => {
    const { result } = renderHook(() => useWorkoutStore())

    const initialIndex = result.current.currentExerciseIndex

    act(() => {
      result.current.setCurrentExerciseById(999)
    })

    expect(result.current.currentExerciseIndex).toBe(initialIndex)
  })

  it('should not reset set index when selecting same exercise', () => {
    const { result } = renderHook(() => useWorkoutStore())

    // Set initial state with exercise and set index
    act(() => {
      result.current.setCurrentExerciseById(123)
      result.current.setCurrentSetIndex(3)
    })

    expect(result.current.currentSetIndex).toBe(3)

    // Select same exercise again
    act(() => {
      result.current.setCurrentExerciseById(123)
    })

    // Set index should remain unchanged
    expect(result.current.currentSetIndex).toBe(3)
  })
})
