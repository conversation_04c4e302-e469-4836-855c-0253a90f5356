import { test, expect } from '@playwright/test'
import { setupAuth } from './helpers/auth-helper'
import { mockWorkoutAPI } from './helpers/workout-api-mocks'

test.describe('Invalid Exercise ID Navigation', () => {
  test.beforeEach(async ({ page, context }) => {
    await setupAuth(context)
    await mockWorkoutAPI(page, true)
  })

  test('should redirect to workout page when navigating to exercise with ID 0', async ({
    page,
  }) => {
    // Try to navigate directly to an exercise with ID 0
    await page.goto('/workout/exercise/0')

    // Should redirect to workout overview page
    await expect(page).toHaveURL('/workout')

    // Verify we're on the workout page
    await expect(page.getByText('Start Workout')).toBeVisible()
  })

  test('should prevent navigation when clicking exercise with invalid ID', async ({
    page,
  }) => {
    // Start from workout page
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Mock a workout with an exercise that has ID 0
    await page.route('**/api/Workout/GetUserWorkout', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            Id: 1,
            Name: 'Test Workout',
            CreatedOn: new Date().toISOString(),
            WorkoutTemplates: [
              {
                Id: 1,
                Label: 'Test Workout',
                Exercises: [
                  {
                    Id: 0, // Invalid ID
                    Label: 'Invalid Exercise',
                    BodyPartId: 1,
                    IsBodyweight: false,
                  },
                  {
                    Id: 123, // Valid ID
                    Label: 'Valid Exercise',
                    BodyPartId: 1,
                    IsBodyweight: false,
                  },
                ],
              },
            ],
          },
        ]),
      })
    })

    // Reload to get the mocked workout
    await page.reload()
    await page.waitForLoadState('networkidle')

    // Try to click on the exercise with ID 0
    const invalidExercise = page.getByText('Invalid Exercise').first()
    if (await invalidExercise.isVisible()) {
      await invalidExercise.click()

      // Should stay on workout page
      await expect(page).toHaveURL('/workout')
    }
  })

  test('should handle invalid exercise ID in timer navigation', async ({
    page,
  }) => {
    // Navigate to rest timer with invalid exercise ID reference
    await page.goto('/workout/rest-timer?between-sets=true')

    // Mock getCurrentExercise to return exercise with ID 0
    await page.evaluate(() => {
      // This simulates a scenario where current exercise has invalid ID
      window.localStorage.setItem(
        'workout-storage',
        JSON.stringify({
          state: {
            currentExerciseIndex: 0,
            exercises: [
              {
                Id: 0, // Invalid ID
                Label: 'Invalid Exercise',
              },
            ],
          },
        })
      )
    })

    // Click skip button
    const skipButton = page.getByRole('button', { name: /skip/i })
    await skipButton.click()

    // Should redirect to workout page instead of /workout/exercise/0
    await expect(page).toHaveURL('/workout')
  })
})
