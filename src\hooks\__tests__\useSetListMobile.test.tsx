import { renderHook } from '@testing-library/react'
import { useSetListMobile } from '../useSetListMobile'
import type { ExerciseModel, RecommendationModel } from '@/types/api'

describe('useSetListMobile', () => {
  const mockExercise: ExerciseModel = {
    Id: 1,
    Label: 'Bench Press',
    IsBodyweight: false,
    IsTimeBased: false,
    IsFlexibility: false,
    IsUnilateral: false,
    IsSystemExercise: true,
    BodyPartId: 1,
  }

  const mockRecommendation: RecommendationModel = {
    ExerciseId: 1,
    ExerciseName: 'Bench Press',
    Series: 3,
    Reps: 10,
    Weight: { Kg: 60, Lb: 132 },
    RIR: 2,
    Increments: { Kg: 2.5, Lb: 5 },
    Min: { Kg: 0, Lb: 0 },
    Max: { Kg: 200, Lb: 440 },
    WarmUpsList: [],
  }

  describe('massUnit parameter', () => {
    it('should use kg when massUnit is kg', () => {
      const { result } = renderHook(() =>
        useSetListMobile(mockExercise, mockRecommendation, 'kg')
      )

      expect(result.current.massUnit).toBe('kg')

      const firstSet = result.current.exerciseWorkSets?.Sets[0]
      expect(firstSet?.WeightSingal).toBe('60.00')
      expect(firstSet?.WeightDouble).toBe('60.00')
    })

    it('should use lb when massUnit is lb', () => {
      const { result } = renderHook(() =>
        useSetListMobile(mockExercise, mockRecommendation, 'lb')
      )

      expect(result.current.massUnit).toBe('lb')

      const firstSet = result.current.exerciseWorkSets?.Sets[0]
      expect(firstSet?.WeightSingal).toBe('132.00')
      expect(firstSet?.WeightDouble).toBe('132.00')
    })

    it('should default to kg when massUnit is not provided', () => {
      const { result } = renderHook(() =>
        useSetListMobile(mockExercise, mockRecommendation)
      )

      expect(result.current.massUnit).toBe('kg')
    })
  })

  describe('null safety for Weight', () => {
    it('should handle null Weight gracefully', () => {
      const recommendationWithNullWeight: RecommendationModel = {
        ...mockRecommendation,
        Weight: null as any,
      }

      const { result } = renderHook(() =>
        useSetListMobile(mockExercise, recommendationWithNullWeight, 'kg')
      )

      const firstSet = result.current.exerciseWorkSets?.Sets[0]
      expect(firstSet?.WeightSingal).toBe('0.00')
      expect(firstSet?.WeightDouble).toBe('0.00')
    })

    it('should handle undefined Weight gracefully', () => {
      const recommendationWithUndefinedWeight: RecommendationModel = {
        ...mockRecommendation,
        Weight: undefined as any,
      }

      const { result } = renderHook(() =>
        useSetListMobile(mockExercise, recommendationWithUndefinedWeight, 'kg')
      )

      const firstSet = result.current.exerciseWorkSets?.Sets[0]
      expect(firstSet?.WeightSingal).toBe('0.00')
      expect(firstSet?.WeightDouble).toBe('0.00')
    })

    it('should handle warmup sets with null Weight', () => {
      const recommendationWithWarmups: RecommendationModel = {
        ...mockRecommendation,
        WarmUpsList: [
          {
            WarmUpReps: 10,
            WarmUpWeightSet: null as any,
          },
        ],
      }

      const { result } = renderHook(() =>
        useSetListMobile(mockExercise, recommendationWithWarmups, 'kg')
      )

      const warmupSet = result.current.exerciseWorkSets?.Sets[0]
      expect(warmupSet?.IsWarmups).toBe(true)
      expect(warmupSet?.WeightSingal).toBe('0.00')
    })
  })

  describe('SetListMobile component weight display', () => {
    it('should safely display weight when Weight is null', () => {
      const setWithNullWeight = {
        Id: 1,
        Weight: null as any,
        Reps: 10,
        IsBodyweight: false,
      }

      // This test is for the component logic that we'll fix
      // The formatWeight function should handle null Weight
      expect(() => {
        const weight = setWithNullWeight.Weight?.Kg || 0
        const formatted = `${weight} kg`
        return formatted
      }).not.toThrow()
    })
  })
})
