'use client'

import React from 'react'
import type { SetType } from '@/utils/setTypeUtils'

interface SetTypeExplainerProps {
  setType: SetType
  isOpen: boolean
}

const setTypeExplanations: Record<
  SetType,
  { title: string; description: string }
> = {
  Normal: {
    title: 'Normal sets',
    description:
      'Standard straight sets where you perform the same weight and aim for the same rep range across all working sets. The most common training method for building strength and muscle.',
  },
  'Rest-pause': {
    title: 'Rest-pause sets',
    description:
      'After reaching failure, take short rest periods (10-15 seconds) and continue for additional reps. This technique maximizes muscle fatigue and growth stimulus. You may see lower rep counts as muscles tire through pauses.',
  },
  'Back-off': {
    title: 'Back-off sets',
    description:
      'After heavy working sets, perform additional sets with lighter weight. This provides extra training volume while allowing recovery from intense efforts.',
  },
  'Drop set': {
    title: 'Drop sets',
    description:
      'Immediately reduce the weight after reaching failure and continue for more reps. This extends the set beyond normal failure for greater muscle exhaustion and growth.',
  },
  Pyramid: {
    title: 'Pyramid sets',
    description:
      'Weight increases while reps decrease with each set. Start lighter with higher reps, building up to your heaviest weight. Great for warming up and building strength.',
  },
  'Reverse pyramid': {
    title: 'Reverse pyramid sets',
    description:
      "Start with your heaviest weight first (after warming up), then reduce weight and increase reps each set. Ideal for strength gains as you're freshest for the heaviest loads.",
  },
}

export function SetTypeExplainer({ setType, isOpen }: SetTypeExplainerProps) {
  // Don't render when closed
  if (!isOpen) {
    return null
  }

  const explanation = setTypeExplanations[setType]

  return (
    <div
      role="region"
      aria-label={`${setType} set explanation`}
      className="bg-brand-primary/5 rounded-theme p-4 mb-4 animate-in slide-in-from-top-2 duration-300"
    >
      <h3 className="font-semibold text-text-primary mb-2">
        {explanation.title}
      </h3>
      <p className="text-sm text-text-secondary leading-relaxed">
        {explanation.description}
      </p>
    </div>
  )
}
