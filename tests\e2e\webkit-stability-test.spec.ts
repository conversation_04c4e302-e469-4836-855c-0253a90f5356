import { test, expect } from '@playwright/test'
import {
  WebKitStabilityHelper,
  setupWebKitStability,
  cleanupWebKitStability,
} from './helpers/webkit-stability'

/**
 * WebKit Stability Test Suite
 * Tests the enhanced WebKit stability measures and error recovery
 */

test.describe('WebKit Stability Tests', () => {
  test.beforeEach(async () => {
    await setupWebKitStability()
  })

  test.afterEach(async () => {
    await cleanupWebKitStability()
  })

  test('should handle browser context creation with retries', async ({
    browser,
  }) => {
    let context = null
    try {
      context = await WebKitStabilityHelper.createStableContext(browser)
      expect(context).toBeTruthy()

      // Verify context is functional
      const page = await context.newPage()
      expect(page).toBeTruthy()
      await page.close()
    } finally {
      if (context) {
        await context.close()
      }
    }
  })

  test('should handle page creation with retries', async ({ browser }) => {
    let context = null
    let page = null
    try {
      context = await WebKitStabilityHelper.createStableContext(browser)
      page = await WebKitStabilityHelper.createStablePage(context)
      expect(page).toBeTruthy()

      // Verify page is functional
      await page.evaluate(() => document.title)
    } finally {
      if (page) await page.close()
      if (context) await context.close()
    }
  })

  test('should handle navigation with retries', async ({ browser }) => {
    let context = null
    let page = null
    try {
      context = await WebKitStabilityHelper.createStableContext(browser)
      page = await WebKitStabilityHelper.createStablePage(context)

      // Test navigation to a simple page
      await WebKitStabilityHelper.navigateStable(
        page,
        'data:text/html,<html><body><h1>Test Page</h1></body></html>'
      )

      // Verify navigation worked
      const title = await page.textContent('h1')
      expect(title).toBe('Test Page')
    } finally {
      if (page) await page.close()
      if (context) await context.close()
    }
  })

  test('should detect browser context errors correctly', async () => {
    const contextError = new Error(
      'Target page, context or browser has been closed'
    )
    expect(WebKitStabilityHelper.isBrowserContextError(contextError)).toBe(true)

    const protocolError = new Error('Protocol error: Connection closed')
    expect(WebKitStabilityHelper.isBrowserContextError(protocolError)).toBe(
      true
    )

    const normalError = new Error('Element not found')
    expect(WebKitStabilityHelper.isBrowserContextError(normalError)).toBe(false)
  })

  test('should detect memory errors correctly', async () => {
    const memoryError = new Error('Out of memory')
    expect(WebKitStabilityHelper.isMemoryError(memoryError)).toBe(true)

    const heapError = new Error('Heap allocation failed')
    expect(WebKitStabilityHelper.isMemoryError(heapError)).toBe(true)

    const normalError = new Error('Element not found')
    expect(WebKitStabilityHelper.isMemoryError(normalError)).toBe(false)
  })

  test('should force garbage collection when available', async () => {
    // This test verifies the garbage collection function doesn't throw
    expect(() => WebKitStabilityHelper.forceGarbageCollection()).not.toThrow()
  })

  test('should wait for memory stabilization', async () => {
    const startTime = Date.now()
    await WebKitStabilityHelper.waitForMemoryStabilization()
    const endTime = Date.now()

    // Should take at least 5 seconds (5000ms for initial wait + 3 GC cycles)
    expect(endTime - startTime).toBeGreaterThan(5000)
  })

  test('should handle memory monitoring lifecycle', async () => {
    // Start monitoring
    WebKitStabilityHelper.startMemoryMonitoring()

    // Wait a bit to ensure monitoring is active
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // Stop monitoring
    WebKitStabilityHelper.stopMemoryMonitoring()

    // Should not throw any errors
    expect(true).toBe(true)
  })

  test('should use withStability wrapper successfully', async ({ browser }) => {
    await WebKitStabilityHelper.withStability(
      'test stability wrapper',
      async (page) => {
        expect(page).toBeTruthy()

        // Test basic page functionality
        await page.goto(
          'data:text/html,<html><body><h1>Stability Test</h1></body></html>'
        )
        const title = await page.textContent('h1')
        expect(title).toBe('Stability Test')
      }
    )({ browser })
  })

  test('should handle multiple page operations with stability', async ({
    browser,
  }) => {
    let context = null
    let page = null

    try {
      context = await WebKitStabilityHelper.createStableContext(browser)
      page = await WebKitStabilityHelper.createStablePage(context)

      // Perform multiple operations to test stability
      await WebKitStabilityHelper.navigateStable(
        page,
        'data:text/html,<html><body><div id="test">Initial</div></body></html>'
      )

      // Test element interaction
      await page.click('#test')
      await page.fill('#test', 'Modified')

      // Test JavaScript execution
      const result = await page.evaluate(() => {
        return document.getElementById('test')?.textContent
      })

      expect(result).toBe('Modified')
    } finally {
      if (page) await page.close()
      if (context) await context.close()
    }
  })

  test('should handle context closure gracefully', async ({ browser }) => {
    let context = null

    try {
      context = await WebKitStabilityHelper.createStableContext(browser)
      const page = await WebKitStabilityHelper.createStablePage(context)

      // Close context while page is still active
      await context.close()
      context = null

      // Attempting to use the page should be handled gracefully
      try {
        await page.goto('data:text/html,<html><body>Test</body></html>')
      } catch (error) {
        // This should be a browser context error
        expect(
          WebKitStabilityHelper.isBrowserContextError(error as Error)
        ).toBe(true)
      }
    } finally {
      if (context) {
        await context.close()
      }
    }
  })
})
