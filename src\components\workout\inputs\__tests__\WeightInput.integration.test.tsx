import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { describe, expect, it, vi } from 'vitest'
import { WeightInput } from '../WeightInput'
import {
  formatWeight,
  roundToNearestIncrement,
  truncateDecimal,
} from '@/utils/weightUtils'

describe('WeightInput Integration Tests', () => {
  describe('Weight formatting with mobile app patterns', () => {
    it('should display weight with 0.## format pattern', () => {
      const testCases = [
        { weight: 100, expected: '100' },
        { weight: 100.5, expected: '100.5' },
        { weight: 100.25, expected: '100.25' },
        { weight: 100.0, expected: '100' },
      ]

      testCases.forEach(({ weight }) => {
        const { rerender } = render(
          <WeightInput
            weight={weight}
            unit="lbs"
            onChange={vi.fn()}
            onIncrement={vi.fn()}
            onDecrement={vi.fn()}
          />
        )

        const input = screen.getByRole('spinbutton', { name: 'Weight' })
        expect(input).toHaveValue(weight)

        // Cleanup for next test
        rerender(<div />)
      })
    })

    it('should format kg values with proper precision', () => {
      const testCases = [
        { weight: 45.36, unit: 'kg' as const, expected: '45.36' },
        { weight: 100, unit: 'kg' as const, expected: '100' },
        { weight: 100.5, unit: 'kg' as const, expected: '100.5' },
      ]

      testCases.forEach(({ weight, unit, expected }) => {
        const { rerender } = render(
          <WeightInput
            weight={weight}
            unit={unit}
            onChange={vi.fn()}
            onIncrement={vi.fn()}
            onDecrement={vi.fn()}
          />
        )

        const input = screen.getByRole('spinbutton', { name: 'Weight' })
        expect(input).toHaveValue(weight)

        // Test that formatWeight would format this correctly
        const formatted = formatWeight({ Lb: 0, Kg: weight }, unit)
        expect(formatted).toBe(`${expected} ${unit}`)

        // Cleanup for next test
        rerender(<div />)
      })
    })
  })

  describe('Weight increment/decrement with plate sizes', () => {
    it('should round to nearest 5lb increment when incrementing in lbs', () => {
      const onChange = vi.fn()
      const onIncrement = vi.fn()

      render(
        <WeightInput
          weight={97.5}
          unit="lbs"
          onChange={onChange}
          onIncrement={onIncrement}
          onDecrement={vi.fn()}
        />
      )

      const incrementButton = screen.getByRole('button', {
        name: 'Increase weight',
      })
      fireEvent.click(incrementButton)

      // Component calls the onIncrement prop, parent would handle the logic
      expect(onIncrement).toHaveBeenCalled()

      // Test that the parent could use roundToNearestIncrement
      const rounded = roundToNearestIncrement(97.5 + 5, 5)
      expect(rounded).toBe(105) // 102.5 rounds to 105 (nearest 5)
    })

    it('should round to nearest 2.5kg increment when incrementing in kg', () => {
      const onChange = vi.fn()
      const onIncrement = vi.fn()

      render(
        <WeightInput
          weight={42.5}
          unit="kg"
          onChange={onChange}
          onIncrement={onIncrement}
          onDecrement={vi.fn()}
        />
      )

      const incrementButton = screen.getByRole('button', {
        name: 'Increase weight',
      })
      fireEvent.click(incrementButton)

      expect(onIncrement).toHaveBeenCalled()

      // Test that the parent could use roundToNearestIncrement
      const rounded = roundToNearestIncrement(42.5 + 2.5, 2.5)
      expect(rounded).toBe(45)
    })

    it('should respect minimum weight of 0', () => {
      const onChange = vi.fn()
      const onDecrement = vi.fn()

      render(
        <WeightInput
          weight={2.5}
          unit="lbs"
          onChange={onChange}
          onIncrement={vi.fn()}
          onDecrement={onDecrement}
        />
      )

      const decrementButton = screen.getByRole('button', {
        name: 'Decrease weight',
      })
      fireEvent.click(decrementButton)

      expect(onDecrement).toHaveBeenCalled()

      // Test that the parent could use roundToNearestIncrement with min
      const rounded = roundToNearestIncrement(2.5 - 5, 5, 0)
      // JavaScript -0 === 0 is true, but Object.is(-0, 0) is false
      // This is expected behavior for Math operations
      expect(Math.abs(rounded)).toBe(0) // -2.5 should be clamped to min of 0
    })

    it('should respect maximum weight of 1000', () => {
      const onChange = vi.fn()
      const onIncrement = vi.fn()

      render(
        <WeightInput
          weight={997.5}
          unit="lbs"
          onChange={onChange}
          onIncrement={onIncrement}
          onDecrement={vi.fn()}
        />
      )

      const incrementButton = screen.getByRole('button', {
        name: 'Increase weight',
      })
      fireEvent.click(incrementButton)

      expect(onIncrement).toHaveBeenCalled()

      // Test that the parent could use roundToNearestIncrement with max
      const rounded = roundToNearestIncrement(997.5 + 5, 5, 0, 1000)
      expect(rounded).toBe(1000)
    })
  })

  describe('Manual weight input with truncation', () => {
    it('should handle decimal input with truncation', () => {
      const onChange = vi.fn()

      render(
        <WeightInput
          weight={100}
          unit="lbs"
          onChange={onChange}
          onIncrement={vi.fn()}
          onDecrement={vi.fn()}
        />
      )

      const input = screen.getByRole('spinbutton', { name: 'Weight' })

      // Simulate user typing a value with many decimals
      fireEvent.change(input, { target: { value: '100.999' } })

      expect(onChange).toHaveBeenCalledWith('100.999')

      // Test that the parent could use truncateDecimal
      const truncated = truncateDecimal(100.999, 2)
      expect(truncated).toBe(100.99)
    })

    it('should handle very small decimal increments', () => {
      const onChange = vi.fn()

      render(
        <WeightInput
          weight={45}
          unit="kg"
          onChange={onChange}
          onIncrement={vi.fn()}
          onDecrement={vi.fn()}
        />
      )

      const input = screen.getByRole('spinbutton', { name: 'Weight' })

      // Simulate user typing a precise value
      fireEvent.change(input, { target: { value: '45.123456' } })

      expect(onChange).toHaveBeenCalledWith('45.123456')

      // Test that the parent could use truncateDecimal for display
      const truncated = truncateDecimal(45.123456, 2)
      expect(truncated).toBe(45.12)
    })
  })

  describe('Bodyweight exercise handling', () => {
    it('should show "Additional Weight" label for bodyweight exercises', () => {
      render(
        <WeightInput
          weight={0}
          unit="lbs"
          onChange={vi.fn()}
          onIncrement={vi.fn()}
          onDecrement={vi.fn()}
          isBodyweight
        />
      )

      expect(screen.getByText('Additional Weight')).toBeInTheDocument()
    })

    it('should allow 0 weight for bodyweight exercises', () => {
      const onChange = vi.fn()

      const { rerender } = render(
        <WeightInput
          weight={0}
          unit="lbs"
          onChange={onChange}
          onIncrement={vi.fn()}
          onDecrement={vi.fn()}
          isBodyweight
        />
      )

      const input = screen.getByRole('spinbutton', { name: 'Weight' })
      expect(input).toHaveValue(0)

      // Test that typing a different value calls onChange
      fireEvent.change(input, { target: { value: '10' } })
      expect(onChange).toHaveBeenCalledWith('10')

      // Simulate weight was updated to 10
      rerender(
        <WeightInput
          weight={10}
          unit="lbs"
          onChange={onChange}
          onIncrement={vi.fn()}
          onDecrement={vi.fn()}
          isBodyweight
        />
      )

      // Now test that we can type 0
      onChange.mockClear()
      fireEvent.change(input, { target: { value: '0' } })
      expect(onChange).toHaveBeenCalledWith('0')
    })
  })

  describe('Mobile-specific formatting scenarios', () => {
    it('should handle fractional plate calculations', () => {
      // Test common fractional plates: 1.25kg, 2.5lbs
      const testCases = [
        {
          weight: 42.5,
          increment: 1.25,
          unit: 'kg' as const,
          expected: 43.75,
        },
        {
          weight: 95,
          increment: 2.5,
          unit: 'lbs' as const,
          expected: 97.5,
        },
      ]

      testCases.forEach(({ weight, increment, expected }) => {
        const rounded = roundToNearestIncrement(weight + increment, increment)
        expect(rounded).toBe(expected)
      })
    })

    it('should format weights matching mobile app display', () => {
      const testCases = [
        {
          weight: { Lb: 100, Kg: 45.36 },
          unit: 'lbs' as const,
          expected: '100 lbs',
        },
        {
          weight: { Lb: 100.5, Kg: 45.59 },
          unit: 'lbs' as const,
          expected: '100.5 lbs',
        },
        {
          weight: { Lb: 220.46, Kg: 100 },
          unit: 'kg' as const,
          expected: '100 kg',
        },
        {
          weight: { Lb: 221.12, Kg: 100.25 },
          unit: 'kg' as const,
          expected: '100.25 kg',
        },
      ]

      testCases.forEach(({ weight, unit, expected }) => {
        const formatted = formatWeight(weight, unit)
        expect(formatted).toBe(expected)
      })
    })
  })
})
