import { describe, it, expect } from 'vitest'
import { createWorkoutSets } from '../createWorkoutSets'
import type {
  ExerciseModel,
  RecommendationModel,
  MultiUnityWeight,
} from '@/types'

// Mock data for tests
const createMockExercise = (
  overrides?: Partial<ExerciseModel>
): ExerciseModel => ({
  Id: 1,
  Label: 'Bench Press',
  IsSystemExercise: false,
  IsSwapTarget: false,
  IsFinished: false,
  BodyPartId: 2,
  IsUnilateral: false,
  IsTimeBased: false,
  EquipmentId: 1,
  IsEasy: false,
  IsMedium: false,
  IsBodyweight: false,
  VideoUrl: '',
  IsNextExercise: false,
  IsPlate: false,
  IsWeighted: true,
  IsPyramid: false,
  IsNormalSets: true,
  WorkoutGroupId: 1,
  IsBodypartPriority: false,
  IsFlexibility: false,
  IsOneHanded: false,
  LocalVideo: '',
  IsAssisted: false,
  ...overrides,
})

const createMockWeight = (kg: number, lb: number): MultiUnityWeight => ({
  Kg: kg,
  Lb: lb,
})

const createMockRecommendation = (
  overrides?: Partial<RecommendationModel>
): RecommendationModel => ({
  ExerciseId: 1,
  Series: 3,
  Reps: 10,
  Weight: createMockWeight(60, 132),
  WarmupsCount: 2,
  RpRest: 0,
  NbPauses: 0,
  NbRepsPauses: 0,
  IsNormalSets: true,
  IsPyramid: false,
  IsReversePyramid: false,
  IsBackOffSet: false,
  Increments: createMockWeight(2, 5),
  Min: createMockWeight(20, 45),
  Max: createMockWeight(200, 440),
  FirstWorkSetReps: 10,
  FirstWorkSetWeight: createMockWeight(60, 132),
  WarmUpsList: [
    {
      WarmUpReps: 5,
      WarmUpWeightSet: createMockWeight(40, 88),
    },
    {
      WarmUpReps: 3,
      WarmUpWeightSet: createMockWeight(50, 110),
    },
  ],
  ...overrides,
})

describe('createWorkoutSets', () => {
  describe('Warm-up Sets', () => {
    it('should generate warm-up sets from WarmUpsList', () => {
      const exercise = createMockExercise()
      const recommendation = createMockRecommendation({
        WarmUpsList: [
          {
            WarmUpReps: 5,
            WarmUpWeightSet: createMockWeight(40, 88),
          },
          {
            WarmUpReps: 3,
            WarmUpWeightSet: createMockWeight(50, 110),
          },
        ],
      })

      const sets = createWorkoutSets(exercise, recommendation, true)

      // Check warm-up sets
      expect(sets.length).toBeGreaterThanOrEqual(2)

      const warmupSets = sets.filter((s) => s.IsWarmups)
      expect(warmupSets).toHaveLength(2)

      // First warm-up set
      expect(warmupSets[0]).toMatchObject({
        ExerciseId: 1,
        Weight: createMockWeight(40, 88),
        Reps: 5,
        IsWarmups: true,
        SetNo: 'W',
        IsFinished: false,
        IsNext: true,
        SetTitle: "Let's warm up:",
      })

      // Last warm-up set
      expect(warmupSets[1]).toMatchObject({
        Weight: createMockWeight(50, 110),
        Reps: 3,
        IsWarmups: true,
        SetNo: 'W',
        IsLastWarmupSet: true,
        SetTitle: 'Last warm-up set:',
      })
    })

    it('should handle empty warm-up list', () => {
      const exercise = createMockExercise()
      const recommendation = createMockRecommendation({
        WarmUpsList: [],
        WarmupsCount: 0,
      })

      const sets = createWorkoutSets(exercise, recommendation, true)
      const warmupSets = sets.filter((s) => s.IsWarmups)

      expect(warmupSets).toHaveLength(0)
    })
  })

  describe('Normal Work Sets', () => {
    it('should generate normal work sets', () => {
      const exercise = createMockExercise()
      const recommendation = createMockRecommendation({
        Series: 3,
        Reps: 10,
        Weight: createMockWeight(60, 132),
        IsNormalSets: true,
        FirstWorkSetReps: 8,
        FirstWorkSetWeight: createMockWeight(55, 121),
      })

      const sets = createWorkoutSets(exercise, recommendation, false)
      const workSets = sets.filter((s) => !s.IsWarmups)

      expect(workSets).toHaveLength(3)

      // First work set
      expect(workSets[0]).toMatchObject({
        ExerciseId: 1,
        Weight: createMockWeight(60, 132),
        Reps: 10,
        IsWarmups: false,
        SetNo: '1',
        IsFirstWorkSet: true,
        SetTitle: 'Working sets:',
        LastTimeSet: 'Last time: 8 x 121 lbs',
      })

      // Middle work sets
      expect(workSets[1]).toMatchObject({
        Weight: createMockWeight(60, 132),
        Reps: 10,
        SetNo: '2',
        SetTitle: '',
      })

      // Last work set
      expect(workSets[2]).toMatchObject({
        Weight: createMockWeight(60, 132),
        Reps: 10,
        SetNo: '3',
        IsLastSet: true,
      })
    })
  })

  describe('Pyramid Sets', () => {
    it('should generate pyramid sets with increasing weight and decreasing reps', () => {
      const exercise = createMockExercise()
      const recommendation = createMockRecommendation({
        Series: 3,
        Reps: 10,
        Weight: createMockWeight(50, 110),
        IsPyramid: true,
        IsNormalSets: false,
        Increments: createMockWeight(2.5, 5),
      })

      const sets = createWorkoutSets(exercise, recommendation, true)
      const workSets = sets.filter((s) => !s.IsWarmups)

      expect(workSets).toHaveLength(3)

      // First set
      expect(workSets[0]).toMatchObject({
        Weight: createMockWeight(50, 110),
        Reps: 10,
        SetTitle: 'Pyramid set:',
      })

      // Second set: 10% heavier, 2 reps less
      expect(workSets[1].Weight.Kg).toBeCloseTo(55, 0)
      expect(workSets[1].Reps).toBe(8)

      // Third set: 10% heavier than second, 2 reps less
      // 55 + 5.5 = 60.5, rounds to 60 with 2.5 increment
      expect(workSets[2].Weight.Kg).toBe(60)
      expect(workSets[2].Reps).toBe(6)
    })
  })

  describe('Reverse Pyramid Sets', () => {
    it('should generate reverse pyramid sets with decreasing weight and increasing reps', () => {
      const exercise = createMockExercise()
      const recommendation = createMockRecommendation({
        Series: 3,
        Reps: 6,
        Weight: createMockWeight(80, 176),
        IsReversePyramid: true,
        IsNormalSets: false,
        Increments: createMockWeight(2.5, 5),
      })

      const sets = createWorkoutSets(exercise, recommendation, true)
      const workSets = sets.filter((s) => !s.IsWarmups)

      expect(workSets).toHaveLength(3)

      // First set (heaviest)
      expect(workSets[0]).toMatchObject({
        Weight: createMockWeight(80, 176),
        Reps: 6,
        SetTitle: 'Reverse pyramid:',
      })

      // Second set: 10% lighter, 2 reps more
      // 80 - 8 = 72, rounds to 72.5 with 2.5 increment
      expect(workSets[1].Weight.Kg).toBe(72.5)
      expect(workSets[1].Reps).toBe(8)

      // Third set: 10% lighter than second, 2 reps more
      // 72.5 - 7.25 = 65.25, rounds to 65 with 2.5 increment
      expect(workSets[2].Weight.Kg).toBe(65)
      expect(workSets[2].Reps).toBe(10)
    })
  })

  describe('Back-off Sets', () => {
    it('should generate back-off sets with reduced weight', () => {
      const exercise = createMockExercise()
      const recommendation = createMockRecommendation({
        Series: 3,
        Reps: 5,
        Weight: createMockWeight(100, 220),
        IsBackOffSet: true,
        BackOffSetWeight: createMockWeight(80, 176),
        IsNormalSets: false,
      })

      const sets = createWorkoutSets(exercise, recommendation, true)
      const workSets = sets.filter((s) => !s.IsWarmups)

      expect(workSets).toHaveLength(3)

      // First set (main set)
      expect(workSets[0]).toMatchObject({
        Weight: createMockWeight(100, 220),
        Reps: 5,
        SetTitle: '',
      })

      // Back-off sets
      expect(workSets[1]).toMatchObject({
        Weight: createMockWeight(80, 176),
        Reps: 7, // 2 reps more
        SetTitle: 'Back-off set:',
        IsBackOffSet: true,
      })

      expect(workSets[2]).toMatchObject({
        Weight: createMockWeight(80, 176),
        Reps: 7,
        SetTitle: 'Back-off set:',
        IsBackOffSet: true,
      })
    })

    it('should calculate back-off weight if not provided', () => {
      const exercise = createMockExercise()
      const recommendation = createMockRecommendation({
        Series: 2,
        Reps: 5,
        Weight: createMockWeight(100, 220),
        IsBackOffSet: true,
        BackOffSetWeight: undefined,
        IsNormalSets: false,
      })

      const sets = createWorkoutSets(exercise, recommendation, true)
      const workSets = sets.filter((s) => !s.IsWarmups)

      // Back-off set should be 80% of main weight
      expect(workSets[1].Weight.Kg).toBeCloseTo(80, 0)
    })
  })

  describe('Rest-Pause Sets', () => {
    it('should generate a single rest-pause set', () => {
      const exercise = createMockExercise()
      const recommendation = createMockRecommendation({
        Series: 1,
        Reps: 8,
        Weight: createMockWeight(70, 154),
        NbPauses: 2,
        IsNormalSets: false,
      })

      const sets = createWorkoutSets(exercise, recommendation, true)
      const workSets = sets.filter((s) => !s.IsWarmups)

      expect(workSets).toHaveLength(1)

      expect(workSets[0]).toMatchObject({
        Weight: createMockWeight(70, 154),
        Reps: 8,
        SetNo: '1',
        SetTitle: 'Rest-pause',
        NbPause: 2,
        IsFirstWorkSet: true,
        IsLastSet: true,
      })
    })
  })

  describe('Weight Unit Formatting', () => {
    it('should format weight in kg when isKg is true', () => {
      const exercise = createMockExercise()
      const recommendation = createMockRecommendation({
        FirstWorkSetWeight: createMockWeight(60, 132),
        FirstWorkSetReps: 10,
      })

      const sets = createWorkoutSets(exercise, recommendation, true)
      const workSets = sets.filter((s) => !s.IsWarmups)

      expect(workSets[0].LastTimeSet).toBe('Last time: 10 x 60 kg')
    })

    it('should format weight in lbs when isKg is false', () => {
      const exercise = createMockExercise()
      const recommendation = createMockRecommendation({
        FirstWorkSetWeight: createMockWeight(60, 132),
        FirstWorkSetReps: 10,
      })

      const sets = createWorkoutSets(exercise, recommendation, false)
      const workSets = sets.filter((s) => !s.IsWarmups)

      expect(workSets[0].LastTimeSet).toBe('Last time: 10 x 132 lbs')
    })
  })

  describe('Exercise Properties', () => {
    it('should include exercise properties in all sets', () => {
      const exercise = createMockExercise({
        IsBodyweight: true,
        IsTimeBased: true,
        IsUnilateral: true,
        IsFlexibility: true,
        BodyPartId: 3,
        EquipmentId: 2,
      })
      const recommendation = createMockRecommendation()

      const sets = createWorkoutSets(exercise, recommendation, true)

      sets.forEach((set) => {
        expect(set).toMatchObject({
          IsBodyweight: true,
          IsTimeBased: true,
          IsUnilateral: true,
          IsFlexibility: true,
          BodypartId: 3,
          EquipmentId: 2,
        })
      })
    })
  })
})
