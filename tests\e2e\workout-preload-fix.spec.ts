import { test, expect } from '@playwright/test'
import { mockSuccessfulLogin } from './helpers/auth'
import {
  mockProgramResponse,
  mockUserWorkoutsResponse,
  mockTodaysWorkoutResponse,
  mockWorkoutRecommendation,
} from './helpers/workout-api-mocks'

test.describe('Workout Preload Fix', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await mockSuccessfulLogin(page)

    // Mock API responses
    await page.route('**/GetUserProgramInfoResponseModel', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockProgramResponse),
      })
    })

    await page.route('**/GetUserWorkouts', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockUserWorkoutsResponse),
      })
    })

    await page.route('**/GetTodaysWorkout', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockTodaysWorkoutResponse),
      })
    })

    // Navigate to program page
    await page.goto('/program')
    await page.waitForLoadState('networkidle')
  })

  test('should wait for exercise recommendations to preload before navigating to first exercise', async ({
    page,
  }) => {
    // Track if recommendations were loaded
    let recommendationLoaded = false

    // Mock recommendation API call
    await page.route('**/GetRecommendation*', async (route) => {
      recommendationLoaded = true
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockWorkoutRecommendation),
      })
    })

    // Navigate to workout page
    await page
      .getByRole('link', { name: /today.*workout|workout.*today/i })
      .click()
    await page.waitForURL('**/workout')

    // Click Start Workout button
    const startButton = page.getByRole('button', { name: /start workout/i })
    await startButton.click()

    // Wait for navigation to exercise page
    await page.waitForURL('**/workout/exercise/**', { timeout: 5000 })

    // Verify that recommendation was loaded BEFORE navigation
    expect(recommendationLoaded).toBe(true)

    // Verify we're on the exercise page
    expect(page.url()).toMatch(/\/workout\/exercise\/\d+/)
  })

  test('should show loading state on Start Workout button during preload', async ({
    page,
  }) => {
    // Add delay to recommendation response to test loading state
    await page.route('**/GetRecommendation*', async (route) => {
      await page.waitForTimeout(500) // Simulate slow API
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockWorkoutRecommendation),
      })
    })

    // Navigate to workout page
    await page
      .getByRole('link', { name: /today.*workout|workout.*today/i })
      .click()
    await page.waitForURL('**/workout')

    // Click Start Workout button
    const startButton = page.getByRole('button', { name: /start workout/i })
    await startButton.click()

    // Button should be disabled during loading
    await expect(startButton).toBeDisabled()

    // Wait for navigation
    await page.waitForURL('**/workout/exercise/**', { timeout: 5000 })

    // Verify we navigated to exercise page
    expect(page.url()).toMatch(/\/workout\/exercise\/\d+/)
  })

  test('should handle preload failure gracefully and still navigate', async ({
    page,
  }) => {
    // Mock recommendation API to fail
    await page.route('**/GetRecommendation*', async (route) => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal Server Error' }),
      })
    })

    // Navigate to workout page
    await page
      .getByRole('link', { name: /today.*workout|workout.*today/i })
      .click()
    await page.waitForURL('**/workout')

    // Click Start Workout button
    const startButton = page.getByRole('button', { name: /start workout/i })
    await startButton.click()

    // Should still navigate to exercise page despite API failure
    await page.waitForURL('**/workout/exercise/**', { timeout: 5000 })

    // Verify we're on the exercise page
    expect(page.url()).toMatch(/\/workout\/exercise\/\d+/)
  })
})
