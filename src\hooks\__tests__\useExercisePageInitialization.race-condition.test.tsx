/**
 * Test race condition scenarios for useExercisePageInitialization
 * Specifically testing the "Exercise Not Available" error that occurs when
 * exercise validation happens before exercises array is loaded
 */

import { renderHook, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { useExercisePageInitialization } from '../useExercisePageInitialization'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useRouter } from 'next/navigation'

// Mock the dependencies
vi.mock('@/hooks/useWorkout', () => ({
  useWorkout: vi.fn(),
}))

vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: vi.fn(),
}))

vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}))

vi.mock('@/utils/debugLog', () => ({
  debugLog: Object.assign(vi.fn(), {
    log: vi.fn(),
    error: vi.fn(),
  }),
}))

describe('useExercisePageInitialization - Race Condition Prevention', () => {
  const mockRouter = {
    replace: vi.fn(),
  }

  const mockWorkoutStore = {
    setCurrentExerciseById: vi.fn(),
    loadingStates: new Map(),
    getCachedExerciseRecommendation: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useRouter as any).mockReturnValue(mockRouter)
    ;(useWorkoutStore as any).mockReturnValue(mockWorkoutStore)
  })

  it('should prevent "Exercise Not Available" error when exercises array is empty', async () => {
    // Mock useWorkout to return empty exercises array (race condition scenario)
    ;(useWorkout as any).mockReturnValue({
      todaysWorkout: [
        {
          WorkoutTemplates: [
            {
              Id: 1,
              Label: 'Test Workout',
            },
          ],
        },
      ],
      isLoadingWorkout: false,
      startWorkout: vi.fn().mockResolvedValue({ success: true }),
      exercises: [], // Empty array - this is the race condition
      workoutSession: { id: 1 },
      loadRecommendation: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
    })

    const { result } = renderHook(() => useExercisePageInitialization(123))

    // Wait for the hook to complete initialization
    await waitFor(() => {
      expect(result.current.isInitializing).toBe(false)
    })

    // Should NOT have an error - the guard clause should prevent validation
    expect(result.current.loadingError).toBeNull()

    // Should NOT attempt to set current exercise when exercises are empty
    expect(mockWorkoutStore.setCurrentExerciseById).not.toHaveBeenCalled()
  })

  it('should prevent "Exercise Not Available" error when exercises is null', async () => {
    // Mock useWorkout to return null exercises (race condition scenario)
    ;(useWorkout as any).mockReturnValue({
      todaysWorkout: [
        {
          WorkoutTemplates: [
            {
              Id: 1,
              Label: 'Test Workout',
            },
          ],
        },
      ],
      isLoadingWorkout: false,
      startWorkout: vi.fn().mockResolvedValue({ success: true }),
      exercises: null, // Null - this is another race condition scenario
      workoutSession: { id: 1 },
      loadRecommendation: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
    })

    const { result } = renderHook(() => useExercisePageInitialization(123))

    // Wait for the hook to complete initialization
    await waitFor(() => {
      expect(result.current.isInitializing).toBe(false)
    })

    // Should NOT have an error - the guard clause should prevent validation
    expect(result.current.loadingError).toBeNull()

    // Should NOT attempt to set current exercise when exercises are null
    expect(mockWorkoutStore.setCurrentExerciseById).not.toHaveBeenCalled()
  })

  it('should show "Exercise Not Available" error when exercise truly does not exist in workout', async () => {
    // Mock useWorkout to return exercises array WITHOUT the requested exercise
    ;(useWorkout as any).mockReturnValue({
      todaysWorkout: [
        {
          WorkoutTemplates: [
            {
              Id: 1,
              Label: 'Test Workout',
            },
          ],
        },
      ],
      isLoadingWorkout: false,
      startWorkout: vi.fn().mockResolvedValue({ success: true }),
      exercises: [
        { Id: 456, Label: 'Different Exercise' }, // Exercise 123 is NOT in the workout
      ],
      workoutSession: { id: 1 },
      loadRecommendation: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
    })

    const { result } = renderHook(() => useExercisePageInitialization(123))

    // Wait for the hook to process
    await waitFor(() => {
      expect(result.current.isInitializing).toBe(false)
    })

    // Should have an error since exercise 123 is not in the workout
    expect(result.current.loadingError).toBeTruthy()
    expect(result.current.loadingError?.message).toContain(
      'Exercise 123 not found in workout'
    )
  })

  it('should successfully initialize when exercise exists in workout', async () => {
    // Mock useWorkout to return exercises array WITH the requested exercise
    ;(useWorkout as any).mockReturnValue({
      todaysWorkout: [
        {
          WorkoutTemplates: [
            {
              Id: 1,
              Label: 'Test Workout',
            },
          ],
        },
      ],
      isLoadingWorkout: false,
      startWorkout: vi.fn().mockResolvedValue({ success: true }),
      exercises: [
        { Id: 123, Label: 'Target Exercise', sets: [] }, // Exercise 123 IS in the workout
      ],
      workoutSession: { id: 1 },
      loadRecommendation: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
    })

    mockWorkoutStore.getCachedExerciseRecommendation.mockReturnValue(null)

    const { result } = renderHook(() => useExercisePageInitialization(123))

    // Wait for the hook to complete initialization
    await waitFor(() => {
      expect(result.current.isInitializing).toBe(false)
    })

    // Should NOT have an error
    expect(result.current.loadingError).toBeNull()

    // Should set the current exercise
    expect(mockWorkoutStore.setCurrentExerciseById).toHaveBeenCalledWith(123)
  })

  it('should prevent validation when workout is still loading', async () => {
    // Mock useWorkout to return loading state
    ;(useWorkout as any).mockReturnValue({
      todaysWorkout: [
        {
          WorkoutTemplates: [
            {
              Id: 1,
              Label: 'Test Workout',
            },
          ],
        },
      ],
      isLoadingWorkout: true, // Still loading
      startWorkout: vi.fn().mockResolvedValue({ success: true }),
      exercises: [{ Id: 123, Label: 'Target Exercise' }], // Exercises exist but still loading
      workoutSession: { id: 1 },
      loadRecommendation: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
    })

    const { result } = renderHook(() => useExercisePageInitialization(123))

    // Wait for the hook to complete initialization
    await waitFor(() => {
      expect(result.current.isInitializing).toBe(false)
    })

    // Should NOT have an error - should wait for loading to complete
    expect(result.current.loadingError).toBeNull()

    // Should NOT attempt to set current exercise while still loading
    expect(mockWorkoutStore.setCurrentExerciseById).not.toHaveBeenCalled()
  })
})
