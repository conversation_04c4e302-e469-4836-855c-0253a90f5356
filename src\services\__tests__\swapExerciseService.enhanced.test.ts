/**
 * Tests for enhanced SwapExerciseService functionality
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { swapExerciseService } from '../swapExerciseService'
import type { ExerciseModel, WorkoutTemplateModel } from '@/types'

// Mock the API
vi.mock('@/api/client', () => ({
  apiClient: {
    post: vi.fn(),
  },
}))

vi.mock('@/lib/auth-utils', () => ({
  getCurrentUserEmail: vi.fn(() => '<EMAIL>'),
}))

describe('Enhanced SwapExerciseService', () => {
  const mockSourceExercise: ExerciseModel = {
    Id: 1,
    Label: 'Bench Press',
    BodyPartId: 1,
    IsSystemExercise: true,
    IsSwapTarget: false,
    IsFinished: false,
    IsUnilateral: false,
    IsTimeBased: false,
    EquipmentId: 1,
    IsEasy: false,
    IsMedium: true,
    IsBodyweight: false,
    VideoUrl: '',
    IsNextExercise: false,
    IsPlate: true,
    IsWeighted: true,
    IsPyramid: false,
    RepsMaxValue: 10,
    RepsMinValue: 6,
    Timer: undefined,
    IsNormalSets: true,
    WorkoutGroupId: undefined,
    IsBodypartPriority: false,
    IsFlexibility: false,
    IsOneHanded: false,
    LocalVideo: '',
    IsAssisted: false,
    SetStyle: 'Normal',
  }

  const mockTargetExercise: ExerciseModel = {
    ...mockSourceExercise,
    Id: 2,
    Label: 'Dumbbell Press',
    EquipmentId: 2,
    IsPlate: false,
  }

  const mockWorkout: WorkoutTemplateModel = {
    Id: 100,
    UserId: 'test-user',
    Label: 'Test Workout',
    Exercises: [mockSourceExercise],
    IsSystemExercise: true,
    WorkoutSettingsModel: {},
  }

  beforeEach(() => {
    vi.clearAllMocks()
    localStorage.clear()
  })

  describe('swapExercise', () => {
    it('should handle temporary swap', async () => {
      const result = await swapExerciseService.swapExercise(
        mockWorkout,
        mockSourceExercise.Id,
        mockTargetExercise,
        { temporary: true, permanent: false }
      )

      expect(result).toEqual({
        workoutId: mockWorkout.Id,
        sourceExercise: mockSourceExercise,
        targetExercise: mockTargetExercise,
        position: 0,
        savedToDb: false,
      })

      // Check that temporary swap was saved to localStorage
      const tempSwaps = swapExerciseService.getTemporarySwaps()
      expect(tempSwaps).toHaveLength(1)
      expect(tempSwaps[0].sourceExerciseId).toBe(mockSourceExercise.Id)
      expect(tempSwaps[0].targetExerciseId).toBe(mockTargetExercise.Id)
    })

    it('should handle permanent swap', async () => {
      const { apiClient } = await import('@/api/client')
      vi.mocked(apiClient.post).mockResolvedValueOnce({
        data: { Result: true, Code: 200 },
      })

      const result = await swapExerciseService.swapExercise(
        mockWorkout,
        mockSourceExercise.Id,
        mockTargetExercise,
        { temporary: false, permanent: true }
      )

      expect(result).toEqual({
        workoutId: mockWorkout.Id,
        sourceExercise: mockSourceExercise,
        targetExercise: mockTargetExercise,
        position: 0,
        savedToDb: true,
      })

      // Check that API was called for permanent swap
      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/Workout/CreateNewUserWorkoutTemplate',
        expect.objectContaining({
          Id: mockWorkout.Id,
          Exercises: [
            expect.objectContaining({
              Id: mockTargetExercise.Id,
              Label: mockTargetExercise.Label,
            }),
          ],
          Username: '<EMAIL>',
        })
      )
    })

    it('should handle both temporary and permanent swap', async () => {
      const { apiClient } = await import('@/api/client')
      vi.mocked(apiClient.post).mockResolvedValueOnce({
        data: { Result: true, Code: 200 },
      })

      const result = await swapExerciseService.swapExercise(
        mockWorkout,
        mockSourceExercise.Id,
        mockTargetExercise,
        { temporary: true, permanent: true }
      )

      expect(result.savedToDb).toBe(true)

      // Check that both temporary and permanent swaps were handled
      const tempSwaps = swapExerciseService.getTemporarySwaps()
      expect(tempSwaps).toHaveLength(1)
      expect(apiClient.post).toHaveBeenCalled()
    })

    it('should throw error if exercise not found', async () => {
      await expect(
        swapExerciseService.swapExercise(
          mockWorkout,
          999, // Non-existent exercise ID
          mockTargetExercise,
          { temporary: true, permanent: false }
        )
      ).rejects.toThrow('Source exercise not found in workout')
    })
  })

  describe('temporary swap management', () => {
    it('should save and retrieve temporary swaps', () => {
      const tempSwaps = swapExerciseService.getTemporarySwaps()
      expect(tempSwaps).toHaveLength(0)

      // Save a temporary swap using the private method (via swapExercise)
      swapExerciseService.swapExercise(
        mockWorkout,
        mockSourceExercise.Id,
        mockTargetExercise,
        { temporary: true, permanent: false }
      )

      const updatedSwaps = swapExerciseService.getTemporarySwaps()
      expect(updatedSwaps).toHaveLength(1)
      expect(updatedSwaps[0].workoutId).toBe(mockWorkout.Id)
    })

    it('should clear temporary swap', async () => {
      // First create a temporary swap
      await swapExerciseService.swapExercise(
        mockWorkout,
        mockSourceExercise.Id,
        mockTargetExercise,
        { temporary: true, permanent: false }
      )

      expect(swapExerciseService.getTemporarySwaps()).toHaveLength(1)

      // Clear the swap
      swapExerciseService.clearTemporarySwap(
        mockWorkout.Id,
        mockSourceExercise.Id
      )

      expect(swapExerciseService.getTemporarySwaps()).toHaveLength(0)
    })

    it('should handle localStorage errors gracefully', () => {
      // Mock localStorage to throw an error
      const originalGetItem = localStorage.getItem
      localStorage.getItem = vi.fn(() => {
        throw new Error('localStorage error')
      })

      const swaps = swapExerciseService.getTemporarySwaps()
      expect(swaps).toEqual([])

      // Restore localStorage
      localStorage.getItem = originalGetItem
    })
  })
})
