import { renderHook, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { useAuth } from '../useAuth'
import { authApi } from '@/api/auth'
import { userProfileApi } from '@/api/userProfile'
import { programApi } from '@/api/program'
import { createQueryWrapper } from '../../../tests/test-utils'
import { debugLog } from '@/utils/debugLog'

vi.mock('@/api/auth')
vi.mock('@/api/userProfile')
vi.mock('@/api/program')
vi.mock('@/utils/userInfoPerformance', () => ({
  startSession: vi.fn(() => 'session-id'),
  endSession: vi.fn(),
}))
vi.mock('@/utils/debugLog', () => ({
  debugLog: Object.assign(
    vi.fn(() => {}),
    {
      warn: vi.fn(),
      error: vi.fn(),
    }
  ),
}))

describe('useAuth smart prefetch', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should silently skip user info prefetch on 404 (new user)', async () => {
    // Given - successful login
    const loginResponse = {
      Token: 'test-token',
      RefreshToken: 'refresh-token',
      user: { id: '123', email: '<EMAIL>' },
    }
    vi.mocked(authApi.login).mockResolvedValue(loginResponse)

    // User info returns 404 (new user)
    vi.mocked(userProfileApi.getUserInfo).mockRejectedValue({
      response: { status: 404 },
      message: 'Not found',
    })

    // When - login
    const { result } = renderHook(() => useAuth(), {
      wrapper: createQueryWrapper(),
    })

    await result.current.login({
      Username: '<EMAIL>',
      Password: 'password',
    })

    // Wait for prefetch
    await waitFor(() => {
      expect(vi.mocked(userProfileApi.getUserInfo)).toHaveBeenCalled()
    })

    // Then - should not log any errors for 404
    expect(vi.mocked(debugLog.warn)).not.toHaveBeenCalled()
    expect(vi.mocked(debugLog.error)).not.toHaveBeenCalled()

    // Login should succeed
    expect(result.current.isAuthenticated).toBe(true)
  })

  it('should silently skip program prefetch when no data exists', async () => {
    // Given - successful login but no program data
    const loginResponse = {
      Token: 'test-token',
      RefreshToken: 'refresh-token',
      user: { id: '123', email: '<EMAIL>' },
    }
    vi.mocked(authApi.login).mockResolvedValue(loginResponse)
    vi.mocked(programApi.getUserProgram).mockResolvedValue(null)

    // When - login
    const { result } = renderHook(() => useAuth(), {
      wrapper: createQueryWrapper(),
    })

    await result.current.login({
      Username: '<EMAIL>',
      Password: 'password',
    })

    // Wait for prefetch
    await waitFor(() => {
      expect(vi.mocked(programApi.getUserProgram)).toHaveBeenCalled()
    })

    // Then - should not log errors for missing data
    expect(vi.mocked(debugLog.warn)).not.toHaveBeenCalled()
    expect(vi.mocked(debugLog.error)).not.toHaveBeenCalled()
  })

  it('should log warnings only for real network errors', async () => {
    // Given - successful login but network error
    const loginResponse = {
      Token: 'test-token',
      RefreshToken: 'refresh-token',
      user: { id: '123', email: '<EMAIL>' },
    }
    vi.mocked(authApi.login).mockResolvedValue(loginResponse)

    // Create error with message containing "network"
    const networkError = new Error('network connection failed')
    vi.mocked(userProfileApi.getUserInfo).mockRejectedValue(networkError)

    // When - login
    const { result } = renderHook(() => useAuth(), {
      wrapper: createQueryWrapper(),
    })

    await result.current.login({
      Username: '<EMAIL>',
      Password: 'password',
    })

    // Wait for prefetch to complete
    await waitFor(() => {
      expect(vi.mocked(userProfileApi.getUserInfo)).toHaveBeenCalled()
    })

    // Wait a bit for async prefetch
    await new Promise((resolve) => setTimeout(resolve, 200))

    // Check that warning was logged
    expect(vi.mocked(debugLog.warn)).toHaveBeenCalledWith(
      '[Login] Prefetch network error:',
      'network connection failed'
    )
  })

  it('should prefetch with reduced delays for better performance', async () => {
    // Given - successful login with all APIs working
    const loginResponse = {
      Token: 'test-token',
      RefreshToken: 'refresh-token',
      user: { id: '123', email: '<EMAIL>' },
    }
    vi.mocked(authApi.login).mockResolvedValue(loginResponse)
    vi.mocked(userProfileApi.getUserInfo).mockResolvedValue({
      Email: '<EMAIL>',
      FirstName: 'Test',
      LastName: 'User',
    })
    vi.mocked(programApi.getUserProgram).mockResolvedValue({
      id: 1,
      name: 'Test Program',
      description: 'Test',
      category: 'strength',
      totalDays: 90,
      currentDay: 1,
      workoutsCompleted: 0,
      startDate: new Date().toISOString(),
      totalWorkouts: 90,
    })

    // Track timing
    const startTime = Date.now()

    // When - login
    const { result } = renderHook(() => useAuth(), {
      wrapper: createQueryWrapper(),
    })

    await result.current.login({
      Username: '<EMAIL>',
      Password: 'password',
    })

    // Wait for all prefetches
    await waitFor(() => {
      expect(vi.mocked(programApi.getUserProgram)).toHaveBeenCalled()
    })

    const totalTime = Date.now() - startTime

    // Then - prefetch should be fast (under 500ms total)
    expect(totalTime).toBeLessThan(500)
  })
})
