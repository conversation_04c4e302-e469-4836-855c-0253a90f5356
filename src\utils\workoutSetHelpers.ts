import type { MultiUnityWeight } from '@/types'

/**
 * Formats weight with unit for display
 */
export function formatWeight(weight: MultiUnityWeight, isKg: boolean): string {
  const value = isKg ? weight.Kg : weight.Lb
  const unit = isKg ? 'kg' : 'lbs'
  return `${Math.round(value)} ${unit}`
}

/**
 * Rounds weight to nearest increment
 */
export function roundToNearestIncrement(
  weight: number,
  increment: number
): number {
  return Math.round(weight / increment) * increment
}
