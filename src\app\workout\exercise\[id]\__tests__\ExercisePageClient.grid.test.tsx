import { render, screen } from '@testing-library/react'
import { vi } from 'vitest'
import { ExercisePageClient } from '../ExercisePageClient'

import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useExercisePageInitialization } from '@/hooks/useExercisePageInitialization'

// Mock dependencies
vi.mock('@/hooks/useWorkout')
vi.mock('@/stores/workoutStore')
vi.mock('@/hooks/useExercisePageInitialization')
vi.mock('@/components/workout/SetScreenWithGrid', () => ({
  SetScreenWithGrid: ({ exerciseId }: { exerciseId: number }) => (
    <div data-testid="set-screen-with-grid">
      SetScreenWithGrid for exercise {exerciseId}
    </div>
  ),
}))

describe('ExercisePageClient - Grid Layout', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // Default mock implementations
    vi.mocked(useWorkout).mockReturnValue({
      isLoadingWorkout: false,
      workoutError: null,
      exercises: [],
      currentWorkout: null,
      todaysWorkout: null,
      isLoadingTodaysWorkout: false,
      todaysWorkoutError: null,
      refetchWorkout: vi.fn(),
      refetchTodaysWorkout: vi.fn(),
    })

    vi.mocked(useWorkoutStore).mockReturnValue({
      loadingStates: new Map(),
    } as any)

    vi.mocked(useExercisePageInitialization).mockReturnValue({
      isInitializing: false,
      loadingError: null,
      retryInitialization: vi.fn(),
    })
  })

  it('should render SetScreenWithGrid when everything is loaded', () => {
    render(<ExercisePageClient exerciseId={123} />)

    // Verify SetScreenWithGrid is rendered
    expect(screen.getByTestId('set-screen-with-grid')).toBeInTheDocument()
    expect(
      screen.getByText('SetScreenWithGrid for exercise 123')
    ).toBeInTheDocument()
  })

  it('should pass exerciseId to SetScreenWithGrid', () => {
    const exerciseId = 456
    render(<ExercisePageClient exerciseId={exerciseId} />)

    expect(
      screen.getByText(`SetScreenWithGrid for exercise ${exerciseId}`)
    ).toBeInTheDocument()
  })

  it('should show loading state when initializing', () => {
    vi.mocked(useExercisePageInitialization).mockReturnValue({
      isInitializing: true,
      loadingError: null,
      retryInitialization: vi.fn(),
    })

    render(<ExercisePageClient exerciseId={123} />)

    // Should show loading state, not the grid
    expect(screen.queryByTestId('set-screen-with-grid')).not.toBeInTheDocument()
  })

  it('should show loading state when loading workout', () => {
    vi.mocked(useWorkout).mockReturnValue({
      isLoadingWorkout: true,
      workoutError: null,
      exercises: [],
      currentWorkout: null,
      todaysWorkout: null,
      isLoadingTodaysWorkout: false,
      todaysWorkoutError: null,
      refetchWorkout: vi.fn(),
      refetchTodaysWorkout: vi.fn(),
    })

    render(<ExercisePageClient exerciseId={123} />)

    // Should show loading state, not the grid
    expect(screen.queryByTestId('set-screen-with-grid')).not.toBeInTheDocument()
  })
})
