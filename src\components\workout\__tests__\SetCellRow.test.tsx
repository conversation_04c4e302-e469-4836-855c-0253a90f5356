import React from 'react'
import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { SetCellRow } from '../SetCellRow'

describe('SetCellRow - Layout', () => {
  const baseProps = {
    setNo: 1,
    reps: 10,
    isFinished: false,
    isBodyweight: false,
    setType: 'Normal' as const,
    onRepsChange: vi.fn(),
    onWeightChange: vi.fn(),
    onSetComplete: vi.fn(),
    onSetTypeBadgeClick: vi.fn(),
    getFormattedWeight: () => '135',
  }

  describe('Set Type Badge Layout', () => {
    it('should stack badge below set number when set type is special', () => {
      render(<SetCellRow {...baseProps} setType="Rest-pause" />)

      // Find the container for set number and badge
      const setNumberText = screen.getByText('1')
      const badge = screen.getByRole('button', { name: /Rest-pause set type/i })

      // Check that they are in a flex column container
      const container = setNumberText.parentElement
      expect(container).toHaveClass(
        'flex',
        'flex-col',
        'items-center',
        'justify-center'
      )

      // Badge should be present
      expect(badge).toBeInTheDocument()
      expect(badge).toHaveTextContent('Rest-pause')
    })

    it('should only show set number for normal sets', () => {
      render(<SetCellRow {...baseProps} setType="Normal" />)

      const setNumberText = screen.getByText('1')
      const badge = screen.queryByRole('button', { name: /set type/i })

      // No badge for normal sets
      expect(badge).not.toBeInTheDocument()

      // Container should still have flex layout for consistency
      const container = setNumberText.parentElement
      expect(container).toHaveClass(
        'flex',
        'flex-col',
        'items-center',
        'justify-center'
      )
    })

    it('should display badge with appropriate styling', () => {
      render(<SetCellRow {...baseProps} setType="Drop set" />)

      const badge = screen.getByRole('button', { name: /Drop set set type/i })

      // Badge should be present with text-xs
      expect(badge).toBeInTheDocument()
      expect(badge).toHaveClass('text-xs')
    })

    it('should use compact variant for badge', () => {
      render(<SetCellRow {...baseProps} setType="Pyramid" />)

      const badge = screen.getByRole('button', { name: /Pyramid set type/i })

      // Badge should use compact styling
      expect(badge).toHaveClass('px-2', 'py-0.5', 'min-h-[32px]', 'mt-1')
    })
  })
})
