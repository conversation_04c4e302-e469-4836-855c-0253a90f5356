// Mock rest-pause API response for testing
// Run this in browser console before navigating to exercise page

// Mock the fetch function to intercept API calls
const originalFetch = window.fetch;

window.fetch = function(url, options) {
    console.log('🌐 Intercepted fetch:', url);
    
    // Check if this is a rest-pause recommendation request
    if (url.includes('/api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew')) {
        console.log('🎯 Intercepting rest-pause recommendation request');
        
        // Return mock rest-pause response
        const mockRestPauseResponse = {
            ExerciseId: 123,
            Series: 1,              // One main working set
            Reps: 12,               // Main set reps
            Weight: { Lb: 135, Kg: 61.2 },
            NbPauses: 3,            // Three rest-pause mini-sets
            NbRepsPauses: 8,        // 8 reps for each mini-set
            RpRest: 15,             // 15 seconds rest between mini-sets
            IsNormalSets: false,    // This is NOT normal sets
            IsPyramid: false,
            IsReversePyramid: false,
            IsBackOffSet: false,
            IsDropSet: false,
            WarmupsCount: 2,
            WarmUpReps1: 5,
            WarmUpReps2: 3,
            WarmUpWeightSet1: { Lb: 95, Kg: 43.1 },
            WarmUpWeightSet2: { Lb: 115, Kg: 52.2 },
            WarmUpsList: [
                {
                    Reps: 5,
                    Weight: { Lb: 95, Kg: 43.1 }
                },
                {
                    Reps: 3,
                    Weight: { Lb: 115, Kg: 52.2 }
                }
            ],
            OneRMProgress: 0,
            RecommendationInKg: 61.2,
            OneRMPercentage: 75,
            IsBodyweight: false,
            IsEasy: false,
            IsMedium: true
        };
        
        console.log('📤 Returning mock rest-pause response:', mockRestPauseResponse);
        
        return Promise.resolve(new Response(JSON.stringify(mockRestPauseResponse), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
        }));
    }
    
    // For all other requests, use original fetch
    return originalFetch.apply(this, arguments);
};

console.log('✅ Rest-pause API mock installed');
console.log('📝 SetStyle:', localStorage.getItem('SetStyle'));
console.log('🔗 Navigate to: http://localhost:3000/workout/exercise/123');
