import { test, expect } from '@playwright/test'
import { setupAuth } from './helpers/auth'
import { mockWorkoutAPI } from './helpers/workout-api-mocks'

test.describe('Exercise Not In Workout Error Handling', () => {
  test.beforeEach(async ({ page, context }) => {
    await setupAuth({ page, context })
    await mockWorkoutAPI(page, true)
  })

  test('should show friendly error when accessing exercise not in workout', async ({
    page,
  }) => {
    // Navigate directly to an exercise ID that doesn't exist in the workout
    await page.goto('/workout/exercise/841')

    // Should show the error boundary with friendly message
    await expect(page.locator('h2')).toContainText('Exercise Not Available')
    await expect(page.locator('p')).toContainText(
      'This exercise is not part of your current workout'
    )

    // Should not show retry button for "not in workout" errors
    await expect(page.locator('button:has-text("Retry")')).not.toBeVisible()

    // Should show back to workout button
    await expect(
      page.locator('button:has-text("Back to Workout")')
    ).toBeVisible()

    // Verify no 404 API calls were made for exercise sets
    const consoleErrors: string[] = []
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text())
      }
    })

    // Wait a bit to ensure no API calls are made
    await page.waitForTimeout(1000)

    // Check that no 404 errors for GetExerciseSets were logged
    const exerciseSet404Errors = consoleErrors.filter((error) =>
      error.includes('GetExerciseSets')
    )
    expect(exerciseSet404Errors).toHaveLength(0)
  })

  test('should navigate back to workout when clicking back button', async ({
    page,
  }) => {
    // Navigate to non-existent exercise
    await page.goto('/workout/exercise/2060')

    // Wait for error boundary
    await expect(page.locator('h2')).toContainText('Exercise Not Available')

    // Click back to workout
    await page.locator('button:has-text("Back to Workout")').click()

    // Should be back on workout page
    await expect(page).toHaveURL('/workout')
  })
})
