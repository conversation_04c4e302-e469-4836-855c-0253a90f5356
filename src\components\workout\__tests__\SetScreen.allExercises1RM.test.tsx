import React from 'react'
import { render, screen } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { SetScreen } from '../SetScreen'
import { NavigationProvider } from '@/contexts/NavigationContext'
import { useSetScreenLogic } from '@/hooks/useSetScreenLogic'
import { useOneRMTracking } from '@/hooks/useOneRMTracking'
import { useAuthStore } from '@/stores/authStore'
import type { RecommendationModel, ExerciseModel } from '@/types/api'

// Mock dependencies
vi.mock('@/contexts/NavigationContext', () => ({
  useNavigation: () => ({
    setTitle: vi.fn(),
  }),
  NavigationProvider: ({ children }: { children: React.ReactNode }) => children,
}))

vi.mock('@/hooks/useSetScreenLogic')
vi.mock('@/hooks/useOneRMTracking')
vi.mock('@/stores/authStore')

// Mock user info
const mockUserInfo = {
  MassUnit: 'kg',
  BodyWeight: { Kg: 80, Lb: 176 },
}

// Mock recommendation
const mockRecommendation: RecommendationModel = {
  Series: 3,
  Reps: 10,
  Weight: { Lb: 198, Kg: 90 },
  FirstWorkSetReps: 10,
  FirstWorkSetWeight: { Lb: 198, Kg: 90 },
  FirstWorkSet1RM: { Lb: 264, Kg: 120 },
  // ... other required fields
} as RecommendationModel

const createMockExercise = (
  overrides: Partial<ExerciseModel>
): ExerciseModel => ({
  Id: 1,
  Label: 'Test Exercise',
  IsSystemExercise: true,
  IsSwapTarget: false,
  IsFinished: false,
  IsUnilateral: false,
  IsTimeBased: false,
  IsEasy: false,
  IsMedium: true,
  IsBodyweight: false,
  VideoUrl: '',
  IsNextExercise: false,
  IsPlate: true,
  IsWeighted: false,
  IsPyramid: false,
  IsNormalSets: true,
  LocalVideo: '',
  IsAssisted: false,
  ...overrides,
})

const mockSetScreenLogic = {
  currentExercise: createMockExercise({}),
  exercises: [],
  currentExerciseIndex: 0,
  isWarmup: false,
  totalSets: 3,
  currentSetIndex: 0,
  setData: { reps: 10, weight: 100, duration: 0 },
  isSaving: false,
  saveError: null,
  showRIRPicker: false,
  showComplete: false,
  showExerciseComplete: false,
  isTransitioning: false,
  showSetSaved: false,
  recommendation: mockRecommendation,
  isLoading: false,
  error: null,
  isLastExercise: false,
  completedSets: [],
  setSetData: vi.fn(),
  handleSaveSet: vi.fn(),
  handleRIRSelect: vi.fn(),
  handleRIRCancel: vi.fn(),
  refetchRecommendation: vi.fn(),
  performancePercentage: vi.fn(() => 75),
  handleSetClick: vi.fn(),
}

const mockOneRMTracking = {
  currentOneRM: 133.3,
  oneRMProgress: 10.5,
  progressMessage: '+10.5%',
  lastTimeInfo: 'Last time: 10 x 90 kg',
  handleWeightChange: vi.fn(),
  handleRepsChange: vi.fn(),
}

describe('SetScreen - 1RM Display for All Exercise Types', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAuthStore).mockReturnValue({
      getCachedUserInfo: () => mockUserInfo,
    } as any)
    vi.mocked(useSetScreenLogic).mockReturnValue(mockSetScreenLogic)
    vi.mocked(useOneRMTracking).mockReturnValue(mockOneRMTracking)
  })

  it('should display 1RM progress for regular weighted exercises', () => {
    render(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    expect(screen.getByTestId('one-rm-display')).toBeInTheDocument()
    expect(screen.getByText('1RM Progress: +10.5%')).toBeInTheDocument()
  })

  it('should display 1RM progress for bodyweight exercises', () => {
    const bodyweightExercise = createMockExercise({
      IsBodyweight: true,
      Label: 'Pull-ups',
    })

    vi.mocked(useSetScreenLogic).mockReturnValue({
      ...mockSetScreenLogic,
      currentExercise: bodyweightExercise,
    })

    render(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    expect(screen.getByTestId('one-rm-display')).toBeInTheDocument()
    expect(screen.getByText('1RM Progress: +10.5%')).toBeInTheDocument()
  })

  it('should display 1RM progress for assisted exercises', () => {
    const assistedExercise = createMockExercise({
      IsAssisted: true,
      IsBodyweight: true,
      Label: 'Assisted Pull-ups',
    })

    vi.mocked(useSetScreenLogic).mockReturnValue({
      ...mockSetScreenLogic,
      currentExercise: assistedExercise,
    })

    render(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    expect(screen.getByTestId('one-rm-display')).toBeInTheDocument()
    expect(screen.getByText('1RM Progress: +10.5%')).toBeInTheDocument()
  })

  it('should display 1RM progress for time-based exercises when weight is used', () => {
    const timeBasedExercise = createMockExercise({
      IsTimeBased: true,
      Label: 'Plank Hold',
    })

    vi.mocked(useSetScreenLogic).mockReturnValue({
      ...mockSetScreenLogic,
      currentExercise: timeBasedExercise,
      setData: { reps: 60, weight: 20, duration: 60 }, // 60 seconds with 20kg weight
    })

    render(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    expect(screen.getByTestId('one-rm-display')).toBeInTheDocument()
    expect(screen.getByText('1RM Progress: +10.5%')).toBeInTheDocument()
  })

  it('should display 1RM progress for all sets, not just first work set', () => {
    // Test for second set
    vi.mocked(useSetScreenLogic).mockReturnValue({
      ...mockSetScreenLogic,
      currentSetIndex: 1, // Second set
    })

    render(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    expect(screen.getByTestId('one-rm-display')).toBeInTheDocument()
    expect(screen.getByText('1RM Progress: +10.5%')).toBeInTheDocument()
  })

  it('should display 1RM progress even for warmup sets', () => {
    vi.mocked(useSetScreenLogic).mockReturnValue({
      ...mockSetScreenLogic,
      isWarmup: true,
    })

    // Mock should still return progress for warmups
    vi.mocked(useOneRMTracking).mockReturnValue({
      ...mockOneRMTracking,
      progressMessage: '+5.0%', // Different progress for warmup
    })

    render(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    expect(screen.getByTestId('one-rm-display')).toBeInTheDocument()
    expect(screen.getByText('1RM Progress: +5.0%')).toBeInTheDocument()
  })
})
