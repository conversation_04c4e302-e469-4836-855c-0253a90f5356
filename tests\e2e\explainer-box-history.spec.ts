import { test, expect } from '@playwright/test'
import { setupAuth } from './helpers/auth'
import {
  createMockRecommendation,
  createMockExercise,
} from './helpers/recommendation-mocks'
import type { RecommendationModel } from '@/types'

test.describe('ExplainerBox Historical Set Display', () => {
  test.beforeEach(async ({ page, context }) => {
    await setupAuth(context)

    // Mock API responses
    await page.route('**/api/userProgramInfo', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          UserProgram: {
            Id: 1,
            Name: 'Test Program',
          },
        },
      })
    })

    await page.route('**/api/GetTodaysWorkout', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          Id: 'workout-1',
          Name: 'Test Workout',
          WorkoutTemplateGroupItems: [
            {
              Id: 1,
              Name: 'Group 1',
              WorkoutItems: [
                {
                  Id: 1,
                  ExerciceId: 1,
                  Sets: [],
                },
              ],
            },
          ],
        },
      })
    })

    await page.route('**/api/SearchMyExercice?id=1', async (route) => {
      await route.fulfill({
        status: 200,
        json: createMockExercise({
          Id: 1,
          Label: 'Bench Press',
        }),
      })
    })
  })

  test('should display correct historical set data following mobile app logic', async ({
    page,
  }) => {
    // Create recommendation following mobile app's HistorySet structure
    const recommendation: RecommendationModel = createMockRecommendation({
      ExerciseId: 1,
      WarmupsCount: 2,
      Series: 3,
      FirstWorkSetReps: 12,
      FirstWorkSetWeight: { Lb: 140, Kg: 63.5 },
      HistorySet: [
        // HistorySet[0] - First warmup
        {
          Id: 1,
          Reps: 5,
          Weight: { Lb: 95, Kg: 43 },
          IsWarmups: true,
          IsNext: false,
          IsFinished: true,
        },
        // HistorySet[1] - Second warmup
        {
          Id: 2,
          Reps: 3,
          Weight: { Lb: 115, Kg: 52 },
          IsWarmups: true,
          IsNext: false,
          IsFinished: true,
        },
        // HistorySet[2] - First work set
        {
          Id: 3,
          Reps: 10,
          Weight: { Lb: 135, Kg: 61 },
          IsWarmups: false,
          IsNext: false,
          IsFinished: true,
        },
        // HistorySet[3] - Second work set
        {
          Id: 4,
          Reps: 9,
          Weight: { Lb: 135, Kg: 61 },
          IsWarmups: false,
          IsNext: false,
          IsFinished: true,
        },
        // HistorySet[4] - Third work set
        {
          Id: 5,
          Reps: 8,
          Weight: { Lb: 135, Kg: 61 },
          IsWarmups: false,
          IsNext: false,
          IsFinished: true,
        },
      ],
    })

    // Mock recommendation API response
    await page.route('**/api/GetRecommendation?*', async (route) => {
      await route.fulfill({
        status: 200,
        json: recommendation,
      })
    })

    // Navigate to exercise page
    await page.goto('/workout/exercise/1')

    // Wait for the page to load
    await page.waitForSelector('[data-testid="exercise-sets-grid"]')

    // Check warmup sets - direct index from HistorySet
    // First warmup should show HistorySet[0]: "Last time: 5 × 95 lbs"
    const firstWarmupExplainer = page.locator('[data-testid="set-0"] + div')
    await expect(firstWarmupExplainer).toContainText('Last time: 5 × 95 lbs')

    // Navigate to next set (second warmup)
    await page
      .locator('[data-testid="set-0"] [data-testid="save-set-button"]')
      .click()

    // Second warmup should show HistorySet[1]: "Last time: 3 × 115 lbs"
    const secondWarmupExplainer = page.locator('[data-testid="set-1"] + div')
    await expect(secondWarmupExplainer).toContainText('Last time: 3 × 115 lbs')

    // Navigate to first work set
    await page
      .locator('[data-testid="set-1"] [data-testid="save-set-button"]')
      .click()

    // First work set should use FirstWorkSetReps/Weight: "Last time: 12 × 140 lbs"
    const firstWorkSetExplainer = page.locator('[data-testid="set-2"] + div')
    await expect(firstWorkSetExplainer).toContainText('Last time: 12 × 140 lbs')

    // Navigate to second work set
    await page
      .locator('[data-testid="set-2"] [data-testid="save-set-button"]')
      .click()

    // Second work set should show filtered work sets[1]: "Last time: 9 × 135 lbs"
    const secondWorkSetExplainer = page.locator('[data-testid="set-3"] + div')
    await expect(secondWorkSetExplainer).toContainText('Last time: 9 × 135 lbs')

    // Navigate to third work set
    await page
      .locator('[data-testid="set-3"] [data-testid="save-set-button"]')
      .click()

    // Third work set should show filtered work sets[2]: "Last time: 8 × 135 lbs"
    const thirdWorkSetExplainer = page.locator('[data-testid="set-4"] + div')
    await expect(thirdWorkSetExplainer).toContainText('Last time: 8 × 135 lbs')
  })

  test('should handle missing historical data gracefully', async ({ page }) => {
    // Create recommendation with no HistorySet
    const recommendation: RecommendationModel = createMockRecommendation({
      ExerciseId: 1,
      WarmupsCount: 2,
      Series: 3,
      HistorySet: [], // No historical data
    })

    // Mock recommendation API response
    await page.route('**/api/GetRecommendation?*', async (route) => {
      await route.fulfill({
        status: 200,
        json: recommendation,
      })
    })

    // Navigate to exercise page
    await page.goto('/workout/exercise/1')

    // Wait for the page to load
    await page.waitForSelector('[data-testid="exercise-sets-grid"]')

    // Check that the ExplainerBox doesn't show "Last time" when no historical data
    const firstSetExplainer = page.locator('[data-testid="set-0"] + div')
    await expect(firstSetExplainer).not.toContainText('Last time:')

    // But should still show the set structure
    await expect(firstSetExplainer).toContainText('2 warm-ups, 3 work sets')
  })
})
