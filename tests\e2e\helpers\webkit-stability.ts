/* eslint-disable no-console, no-await-in-loop */
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON>ontext, Page, test } from '@playwright/test'
import { webkitContextOptions, webkitTestOptions } from '../webkit-config'

/**
 * WebKit stability helper for handling browser crashes and context issues
 * Enhanced with memory management and improved error recovery
 */
export class WebKitStabilityHelper {
  private static readonly MAX_RETRIES = 5 // Increased retries

  private static readonly RETRY_DELAY = 3000 // Increased delay

  private static readonly MEMORY_CHECK_INTERVAL = 30000 // 30 seconds

  private static memoryMonitor: NodeJS.Timeout | null = null

  /**
   * Start memory monitoring for WebKit stability
   */
  static startMemoryMonitoring(): void {
    if (this.memoryMonitor) {
      clearInterval(this.memoryMonitor)
    }

    this.memoryMonitor = setInterval(() => {
      const memUsage = process.memoryUsage()
      const memUsageMB = {
        rss: Math.round(memUsage.rss / 1024 / 1024),
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
        external: Math.round(memUsage.external / 1024 / 1024),
      }

      console.log(
        `📊 Memory usage: RSS=${memUsageMB.rss}MB, Heap=${memUsageMB.heapUsed}/${memUsageMB.heapTotal}MB, External=${memUsageMB.external}MB`
      )

      // Force garbage collection if heap usage is high
      if (memUsageMB.heapUsed > 1024 && global.gc) {
        console.log('🗑️ Forcing garbage collection due to high memory usage')
        global.gc()
      }
    }, this.MEMORY_CHECK_INTERVAL)
  }

  /**
   * Stop memory monitoring
   */
  static stopMemoryMonitoring(): void {
    if (this.memoryMonitor) {
      clearInterval(this.memoryMonitor)
      this.memoryMonitor = null
    }
  }

  /**
   * Force garbage collection if available
   */
  static forceGarbageCollection(): void {
    if (global.gc) {
      console.log('🗑️ Forcing garbage collection')
      global.gc()
    } else {
      console.log('⚠️ Garbage collection not available (run with --expose-gc)')
    }
  }

  /**
   * Create a new browser context with retry logic for WebKit stability
      // eslint-disable-next-line no-await-in-loop
   */
  static async createStableContext(browser: Browser): Promise<BrowserContext> {
    const createContextWithRetry = async (): Promise<BrowserContext> => {
      let lastError: Error | null = null

      for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
        try {
          console.log(
            `🔧 Creating browser context (attempt ${attempt}/${this.MAX_RETRIES})`
          )

          // Force garbage collection before creating context
          this.forceGarbageCollection()

          const context = await browser.newContext({
            ...webkitContextOptions,
            // Additional stability options
            recordVideo: {
              dir: 'test-results/videos',
              size: { width: 390, height: 844 },
            },
            recordHar: {
              path: `test-results/har/context-${Date.now()}.har`,
              mode: 'minimal',
            },
          })

          // Add context error handlers
          context.on('close', () => {
            console.log('🔒 Browser context closed')
          })

          // Set up page error handling for all pages in this context
          context.on('page', (page) => {
            page.on('crash', () => {
              console.error('💥 Page crashed in context!')
            })

            page.on('pageerror', (error) => {
              console.error('🚨 Page error in context:', error.message)
            })

            page.on('requestfailed', (request) => {
              console.warn(
                '🌐 Request failed:',
                request.url(),
                request.failure()?.errorText
              )
            })
          })
          // eslint-disable-next-line no-await-in-loop

          console.log('✅ Browser context created successfully')
          return context
        } catch (error) {
          lastError = error as Error
          // eslint-disable-next-line no-await-in-loop
          console.error(
            `❌ Browser context creation failed (attempt ${attempt}):`,
            error
          )

          // Check if it's a memory-related error
          if (this.isMemoryError(error as Error)) {
            console.log('🧠 Memory-related error detected, forcing cleanup')
            this.forceGarbageCollection()
            await this.waitForMemoryStabilization()
          }

          if (attempt < this.MAX_RETRIES) {
            console.log(`🔄 Retrying in ${this.RETRY_DELAY}ms...`)
            await new Promise((resolve) =>
              setTimeout(resolve, this.RETRY_DELAY)
            )
          }
          // eslint-disable-next-line no-await-in-loop
        }
      }

      throw new Error(
        `Failed to create browser context after ${this.MAX_RETRIES} attempts. Last error: ${lastError?.message}`
      )
    }

    return createContextWithRetry()
  }

  /**
   * Create a new page with retry logic for WebKit stability
   */
  static async createStablePage(context: BrowserContext): Promise<Page> {
    const createPageWithRetry = async (): Promise<Page> => {
      let lastError: Error | null = null

      for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
        try {
          console.log(
            `🔧 Creating page (attempt ${attempt}/${this.MAX_RETRIES})`
          )

          // Force garbage collection before creating page
          this.forceGarbageCollection()

          const page = await context.newPage()

          // Enhanced error handling for page crashes
          page.on('crash', () => {
            console.error('💥 Page crashed!')
            this.forceGarbageCollection()
          })

          page.on('pageerror', (error) => {
            console.error('🚨 Page error:', error.message)
          })

          page.on('requestfailed', (request) => {
            console.warn(
              '🌐 Request failed:',
              // eslint-disable-next-line no-await-in-loop
              request.url(),
              request.failure()?.errorText
            )
          })

          // eslint-disable-next-line no-await-in-loop
          page.on('console', (msg) => {
            if (msg.type() === 'error') {
              console.error('🖥️ Console error:', msg.text())
            }
          })

          // Set additional page options for stability
          await page.setExtraHTTPHeaders(
            webkitTestOptions.extraHTTPHeaders || {}
          )

          console.log('✅ Page created successfully')
          return page
        } catch (error) {
          lastError = error as Error
          console.error(`❌ Page creation failed (attempt ${attempt}):`, error)

          // Check if it's a memory-related error
          // eslint-disable-next-line no-await-in-loop
          if (this.isMemoryError(error as Error)) {
            console.log('🧠 Memory-related error detected, forcing cleanup')
            this.forceGarbageCollection()
            await this.waitForMemoryStabilization()
          }

          if (attempt < this.MAX_RETRIES) {
            console.log(`🔄 Retrying in ${this.RETRY_DELAY}ms...`)
            await new Promise((resolve) =>
              setTimeout(resolve, this.RETRY_DELAY)
            )
          }
        }
      }

      throw new Error(
        `Failed to create page after ${this.MAX_RETRIES} attempts. Last error: ${lastError?.message}`
      )
    }

    return createPageWithRetry()
  }
  // eslint-disable-next-line no-await-in-loop

  /**
   * Navigate to a URL with retry logic for WebKit stability
   */
  static async navigateStable(page: Page, url: string): Promise<void> {
    // eslint-disable-next-line no-await-in-loop
    const navigateWithRetry = async (): Promise<void> => {
      let lastError: Error | null = null

      for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
        try {
          console.log(
            `🔧 Navigating to ${url} (attempt ${attempt}/${this.MAX_RETRIES})`
          )

          await page.goto(url, {
            timeout: webkitTestOptions.navigationTimeout || 90000,
            waitUntil: 'domcontentloaded',
          })

          // Wait for page to stabilize
          await page.waitForLoadState('networkidle', { timeout: 10000 })

          console.log('✅ Navigation successful')
          return
        } catch (error) {
          lastError = error as Error
          console.error(`❌ Navigation failed (attempt ${attempt}):`, error)

          // Check if it's a memory-related error
          if (this.isMemoryError(error as Error)) {
            console.log('🧠 Memory-related error detected during navigation')
            this.forceGarbageCollection()
            await this.waitForMemoryStabilization()
          }

          if (attempt < this.MAX_RETRIES) {
            console.log(`🔄 Retrying navigation in ${this.RETRY_DELAY}ms...`)
            await new Promise((resolve) =>
              setTimeout(resolve, this.RETRY_DELAY)
            )
          }
        }
      }

      throw new Error(
        `Failed to navigate to ${url} after ${this.MAX_RETRIES} attempts. Last error: ${lastError?.message}`
      )
    }

    await navigateWithRetry()
  }

  /**
   * Check if an error is related to browser context closure
   */
  // eslint-disable-next-line no-await-in-loop
  static isBrowserContextError(error: Error): boolean {
    const errorMessage = error.message.toLowerCase()
    // eslint-disable-next-line no-await-in-loop
    return (
      errorMessage.includes('browser has been closed') ||
      errorMessage.includes('context has been closed') ||
      errorMessage.includes(
        'target page, context or browser has been closed'
      ) ||
      errorMessage.includes('browsercontext.newpage') ||
      errorMessage.includes('protocol error') ||
      errorMessage.includes('connection closed') ||
      errorMessage.includes('websocket connection') ||
      errorMessage.includes('target closed')
    )
  }

  /**
   * Check if an error is related to memory issues
   */
  static isMemoryError(error: Error): boolean {
    const errorMessage = error.message.toLowerCase()
    return (
      errorMessage.includes('out of memory') ||
      errorMessage.includes('memory') ||
      errorMessage.includes('heap') ||
      errorMessage.includes('allocation failed') ||
      errorMessage.includes('cannot allocate')
    )
  }

  /**
   * Wait for memory to stabilize
   */
  static async waitForMemoryStabilization(): Promise<void> {
    console.log('⏳ Waiting for memory stabilization...')
    await new Promise((resolve) => setTimeout(resolve, 5000))

    // Force multiple garbage collections
    for (let i = 0; i < 3; i++) {
      this.forceGarbageCollection()
      await new Promise((resolve) => setTimeout(resolve, 1000))
    }
  }

  /**
   * Playwright test wrapper with WebKit stability handling
   */
  static withStability(
    testName: string,
    testFn: (page: Page) => Promise<void>
  ) {
    return test(testName, async ({ browser }) => {
      let context: BrowserContext | null = null
      let page: Page | null = null

      try {
        // Start memory monitoring
        this.startMemoryMonitoring()

        context = await this.createStableContext(browser)
        page = await this.createStablePage(context)

        await testFn(page)
      } catch (error) {
        if (this.isBrowserContextError(error as Error)) {
          console.error('🚨 Browser context error detected:', error)
          throw new Error(
            `WebKit browser context failure: ${(error as Error).message}`
          )
        }
        if (this.isMemoryError(error as Error)) {
          console.error('🧠 Memory error detected:', error)
          throw new Error(`WebKit memory failure: ${(error as Error).message}`)
        }
        throw error
      } finally {
        try {
          if (page) {
            await page.close()
          }
          if (context) {
            await context.close()
          }
        } catch (cleanupError) {
          console.warn('⚠️ Cleanup error (non-fatal):', cleanupError)
        } finally {
          // Stop memory monitoring and force final cleanup
          this.stopMemoryMonitoring()
          this.forceGarbageCollection()
        }
      }
    })
  }
}

/**
 * Test hook for WebKit stability - use in beforeEach
 */
export async function setupWebKitStability() {
  console.log('🔧 Setting up WebKit stability measures')
  WebKitStabilityHelper.startMemoryMonitoring()
  WebKitStabilityHelper.forceGarbageCollection()
}

/**
 * Test hook for WebKit cleanup - use in afterEach
 */
export async function cleanupWebKitStability() {
  console.log('🧹 Cleaning up WebKit stability measures')
  WebKitStabilityHelper.forceGarbageCollection()
  await WebKitStabilityHelper.waitForMemoryStabilization()
}
