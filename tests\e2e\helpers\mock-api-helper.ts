import { Page } from '@playwright/test'
import type { WorkoutTemplateGroupModel, WorkoutLogSerieModel } from '@/types'

export class MockApiHelper {
  constructor(private page: Page) {}

  async mockAuth() {
    await this.page.route('**/api/Account/Login*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Token: 'mock-jwt-token',
          RefreshToken: 'mock-refresh-token',
          User: {
            Email: '<EMAIL>',
            FirstName: 'Test',
            LastName: 'User',
          },
        }),
      })
    })

    await this.page.addInitScript(() => {
      const authState = {
        state: {
          user: {
            email: '<EMAIL>',
            FirstName: 'Test',
            LastName: 'User',
          },
          isAuthenticated: true,
          token: 'mock-jwt-token',
          refreshToken: 'mock-refresh-token',
          cachedUserInfo: null,
          cacheVersion: 1,
        },
        version: 0,
      }
      window.localStorage.setItem('drmuscle-auth', JSON.stringify(authState))
    })

    await this.page.route('**/api/Account/GetUserInfo*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Email: '<EMAIL>',
          FirstName: 'Test',
          LastName: 'User',
          MassUnit: 'lbs',
        }),
      })
    })
  }

  async mockUserWorkout(workout: WorkoutTemplateGroupModel) {
    await this.page.route(
      '**/api/Workout/GetUserWorkoutTemplateGroup*',
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([workout]),
        })
      }
    )
  }

  async mockGetUserProgramInfo() {
    await this.page.route(
      '**/api/Workout/GetUserProgramInfo*',
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            GetUserProgramInfoResponseModel: {
              RecommendedProgram: {
                Id: 1,
                Label: 'Test Program',
                RemainingToLevelUp: 3,
              },
              NextWorkoutTemplate: {
                Id: 101,
                Label: 'Workout A',
                IsSystemExercise: false,
              },
            },
            TotalWorkoutCompleted: 10,
            ConsecutiveWeeks: 3,
          }),
        })
      }
    )
  }

  async mockWorkoutOperations() {
    // Mock starting a workout session
    await this.page.route(
      '**/api/WorkoutSession/StartWorkoutSession*',
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            Id: 1,
            WorkoutTemplateId: 101,
            StartTime: new Date().toISOString(),
            UserId: 'test-user-123',
          }),
        })
      }
    )

    await this.page.route('**/api/Workout/StartWorkout*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Id: 1,
          WorkoutTemplateId: 101,
          StartTime: new Date().toISOString(),
        }),
      })
    })

    await this.page.route(
      '**/api/Recommendation/GetRecommendation*',
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            Reps: 10,
            Weight: { Lb: 100, Kg: 45.4 },
            RIR: 2,
          }),
        })
      }
    )
  }

  async mockExerciseSets(
    exerciseId: number,
    sets: Partial<WorkoutLogSerieModel>[]
  ) {
    await this.page.route(
      `**/api/Exercise/GetUserWorkoutSets?exerciseId=${exerciseId}*`,
      async (route) => {
        const mockSets: WorkoutLogSerieModel[] = sets.map((set, index) => ({
          Id: set.Id || index + 1,
          ExerciseId: exerciseId,
          Reps: set.Reps || 10,
          Weight: set.Weight || { Lb: 100, Kg: 45.4 },
          IsWarmups: set.IsWarmups || false,
          IsFinished: set.IsFinished || false,
          IsNext: set.IsNext || false,
          ...set,
        }))

        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(mockSets),
        })
      }
    )

    // Mock GetExercisesSets endpoint that returns all sets for exercises
    await this.page.route(
      '**/api/Exercise/GetExercisesSets*',
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([]),
        })
      }
    )
  }
}
