import type { RecommendationModel } from '@/types'

export type SetType =
  | 'Rest-pause'
  | 'Back-off'
  | 'Drop set'
  | 'Pyramid'
  | 'Reverse pyramid'
  | 'Normal'

/**
 * Determines the set type based on the recommendation model
 */
export function getSetType(recommendation: RecommendationModel): SetType {
  // Rest-pause sets
  if (!recommendation.IsNormalSets && recommendation.NbPauses > 0) {
    return 'Rest-pause'
  }

  // Back-off sets
  if (recommendation.IsBackOffSet) {
    return 'Back-off'
  }

  // Drop sets
  if (recommendation.IsDropSet) {
    return 'Drop set'
  }

  // Pyramid sets
  if (recommendation.IsPyramid) {
    return 'Pyramid'
  }

  // Reverse pyramid sets
  if (recommendation.IsReversePyramid) {
    return 'Reverse pyramid'
  }

  // Normal sets
  return 'Normal'
}

/**
 * Gets the display title for a set type
 */
export function getSetTypeTitle(setType: SetType): string | undefined {
  return setType
}
