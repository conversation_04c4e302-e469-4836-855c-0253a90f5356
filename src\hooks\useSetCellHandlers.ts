import React from 'react'

interface SetData {
  IsBackOffSet?: boolean
  IsWarmups?: boolean
  IsFinished?: boolean
  IsEditing?: boolean
  IsFirstWorkSet?: boolean
}

interface UseSetCellHandlersProps {
  reps: number
  weight: number
  exercise?: {
    Id?: number
    Name?: string
    IsBodyweight?: boolean
  }
  recommendation?: {
    ExerciseId?: number
    Weight?: { Lb: number; Kg: number }
    Reps?: number
    Series?: number
  }
  setData?: SetData
  userBodyWeight: number
  unit: 'kg' | 'lbs'
  isNext: boolean
  isFinished: boolean
  isBodyweight: boolean
  onRepsChange?: (reps: number) => void
  onWeightChange?: (weight: number) => void
  onOneRMUpdate?: (data: {
    weight: number
    reps: number
    exercise?: {
      Id?: number
      Name?: string
      IsBodyweight?: boolean
    }
    recommendation?: {
      ExerciseId?: number
      Weight?: { Lb: number; Kg: number }
      Reps?: number
      Series?: number
    }
    isKg: boolean
    userBodyWeight: number
    isFirstWorkSet?: boolean
  }) => void
}

export function useSetCellHandlers({
  reps,
  weight,
  exercise,
  recommendation,
  setData,
  userBodyWeight,
  unit,
  isNext,
  isFinished,
  isBodyweight,
  onRepsChange,
  onWeightChange,
  onOneRMUpdate,
}: UseSetCellHandlersProps) {
  const handleRepsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target

    // Use MAUI-style validation
    if (value.endsWith(',') || value.endsWith('.') || value === '') return

    const numValue = parseInt(value.replace(',', '').replace('.', ''), 10)
    if (!Number.isNaN(numValue)) {
      onRepsChange?.(numValue)

      // Trigger 1RM update for first work sets (matching MAUI logic)
      if (
        setData &&
        !setData.IsBackOffSet &&
        !setData.IsWarmups &&
        !setData.IsFinished &&
        !setData.IsEditing
      ) {
        onOneRMUpdate?.({
          weight,
          reps: numValue,
          exercise,
          recommendation,
          isKg: unit === 'kg',
          userBodyWeight,
          isFirstWorkSet: setData.IsFirstWorkSet,
        })
      }
    }
  }

  const handleWeightChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target

    if (value === '') return

    // Replace comma with dot and remove spaces (MAUI style)
    const entryText = value.replace(',', '.').replace(' ', '')
    const numValue = parseFloat(entryText)

    if (!Number.isNaN(numValue) && !entryText.endsWith('.')) {
      onWeightChange?.(numValue)

      // Trigger 1RM update for first work sets (matching MAUI logic)
      if (
        setData &&
        !setData.IsBackOffSet &&
        !setData.IsWarmups &&
        !setData.IsFinished &&
        !setData.IsEditing
      ) {
        onOneRMUpdate?.({
          weight: numValue,
          reps,
          exercise,
          recommendation,
          isKg: unit === 'kg',
          userBodyWeight,
          isFirstWorkSet: setData.IsFirstWorkSet,
        })
      }
    }
  }

  const handleRepsArrowClick = (direction: 'up' | 'down') => {
    if (!isNext || isFinished) return

    if (direction === 'down' && (reps || 0) <= 1) {
      // Don't allow reps to go below 1
      return
    }

    const newReps = direction === 'up' ? (reps || 0) + 1 : (reps || 0) - 1
    onRepsChange?.(newReps)
  }

  const handleWeightArrowClick = (direction: 'up' | 'down') => {
    if (!isNext || isFinished || isBodyweight) return

    const increment = unit === 'kg' ? 2.5 : 5

    if (direction === 'down' && (weight || 0) - increment < 0) {
      // Don't allow weight to go negative
      return
    }

    const newWeight =
      direction === 'up' ? (weight || 0) + increment : (weight || 0) - increment
    onWeightChange?.(newWeight)
  }

  return {
    handleRepsChange,
    handleWeightChange,
    handleRepsArrowClick,
    handleWeightArrowClick,
  }
}
