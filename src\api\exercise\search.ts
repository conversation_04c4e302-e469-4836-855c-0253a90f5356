/**
 * Exercise search API functions
 * For finding alternative exercises for swapping
 */

import { apiClient } from '../client'
import type { ExerciseModel } from '@/types'
import { logger } from '@/utils/logger'

/**
 * Mock exercise alternatives for development
 */
function getMockExerciseAlternatives(
  bodyPartId: number,
  currentExerciseId?: number
): ExerciseModel[] {
  const mockExercises: ExerciseModel[] = [
    {
      Id: 101,
      Label: 'Dumbbell Press',
      IsSystemExercise: true,
      IsSwapTarget: false,
      IsFinished: false,
      BodyPartId: bodyPartId,
      IsUnilateral: false,
      IsTimeBased: false,
      EquipmentId: 1,
      IsEasy: false,
      IsMedium: true,
      IsBodyweight: false,
      VideoUrl: '',
      IsNextExercise: false,
      IsPlate: false,
      IsWeighted: true,
      IsPyramid: false,
      RepsMaxValue: 12,
      RepsMinValue: 8,
      Timer: undefined,
      IsNormalSets: true,
      WorkoutGroupId: undefined,
      IsBodypartPriority: false,
      IsFlexibility: false,
      IsOneHanded: false,
      LocalVideo: '',
      IsAssisted: false,
      SetStyle: 'Normal',
    },
    {
      Id: 102,
      Label: 'Incline Bench Press',
      IsSystemExercise: true,
      IsSwapTarget: false,
      IsFinished: false,
      BodyPartId: bodyPartId,
      IsUnilateral: false,
      IsTimeBased: false,
      EquipmentId: 2,
      IsEasy: false,
      IsMedium: false,
      IsBodyweight: false,
      VideoUrl: '',
      IsNextExercise: false,
      IsPlate: true,
      IsWeighted: true,
      IsPyramid: false,
      RepsMaxValue: 10,
      RepsMinValue: 6,
      Timer: undefined,
      IsNormalSets: true,
      WorkoutGroupId: undefined,
      IsBodypartPriority: false,
      IsFlexibility: false,
      IsOneHanded: false,
      LocalVideo: '',
      IsAssisted: false,
      SetStyle: 'Normal',
    },
    {
      Id: 103,
      Label: 'Push-ups',
      IsSystemExercise: true,
      IsSwapTarget: false,
      IsFinished: false,
      BodyPartId: bodyPartId,
      IsUnilateral: false,
      IsTimeBased: false,
      EquipmentId: 0,
      IsEasy: true,
      IsMedium: false,
      IsBodyweight: true,
      VideoUrl: '',
      IsNextExercise: false,
      IsPlate: false,
      IsWeighted: false,
      IsPyramid: false,
      RepsMaxValue: 20,
      RepsMinValue: 10,
      Timer: undefined,
      IsNormalSets: true,
      WorkoutGroupId: undefined,
      IsBodypartPriority: false,
      IsFlexibility: false,
      IsOneHanded: false,
      LocalVideo: '',
      IsAssisted: false,
      SetStyle: 'Normal',
    },
  ]

  return mockExercises.filter((ex) => ex.Id !== currentExerciseId)
}

/**
 * Get exercises by body part for exercise swapping
 * @param bodyPartId - The body part ID to filter exercises
 * @param currentExerciseId - Current exercise ID to exclude from results
 * @returns Array of alternative exercises
 */
export async function getExercisesByBodyPart(
  bodyPartId: number,
  currentExerciseId?: number
): Promise<ExerciseModel[]> {
  try {
    logger.log(
      `[ExerciseSearch] Fetching exercises for body part: ${bodyPartId}`
    )

    // Use the existing exercise endpoint that might return exercises by body part
    // This is a placeholder - you may need to adjust based on your actual API
    const response = await apiClient.get(
      `/api/Exercise/GetByBodyPart/${bodyPartId}`
    )

    let exercises: ExerciseModel[] = []

    if (response.data?.Result) {
      exercises = response.data.Result
    } else if (Array.isArray(response.data)) {
      exercises = response.data
    } else {
      logger.warn('[ExerciseSearch] Unexpected response format:', response.data)
      return []
    }

    // Filter out the current exercise if provided
    if (currentExerciseId) {
      exercises = exercises.filter((ex) => ex.Id !== currentExerciseId)
    }

    // Sort by system exercises first, then by name
    exercises.sort((a, b) => {
      if (a.IsSystemExercise && !b.IsSystemExercise) return -1
      if (!a.IsSystemExercise && b.IsSystemExercise) return 1
      return a.Label.localeCompare(b.Label)
    })

    logger.log(
      `[ExerciseSearch] Found ${exercises.length} alternative exercises`
    )
    return exercises
  } catch (error) {
    logger.error(
      '[ExerciseSearch] Failed to fetch exercises by body part:',
      error
    )

    // Return mock data for development/testing
    if (process.env.NODE_ENV === 'development') {
      return getMockExerciseAlternatives(bodyPartId, currentExerciseId)
    }

    return []
  }
}

/**
 * Search exercises by name (for future use)
 * @param query - Search query
 * @param bodyPartId - Optional body part filter
 * @returns Array of matching exercises
 */
export async function searchExercisesByName(
  query: string,
  bodyPartId?: number
): Promise<ExerciseModel[]> {
  try {
    const params = new URLSearchParams({
      q: query,
      ...(bodyPartId && { bodyPartId: bodyPartId.toString() }),
    })

    const response = await apiClient.get(`/api/Exercise/Search?${params}`)

    if (response.data?.Result) {
      return response.data.Result
    } else if (Array.isArray(response.data)) {
      return response.data
    }

    return []
  } catch (error) {
    logger.error('[ExerciseSearch] Failed to search exercises:', error)
    return []
  }
}
