import { render, screen } from '@testing-library/react'
import { vi } from 'vitest'
import { WorkoutComplete } from '../WorkoutComplete'
import { useUserStatsStore } from '@/stores/userStatsStore'

// Mock ShareButton to capture props
let shareButtonProps: any = null
vi.mock('@/components/navigation/ShareButton', () => ({
  ShareButton: vi.fn((props: any) => {
    shareButtonProps = props
    return (
      <button aria-label="Share">
        <span>Share</span>
      </button>
    )
  }),
}))

// Mock the hooks
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(() => ({
    push: vi.fn(),
  })),
}))

vi.mock('@/hooks/useWorkout', () => ({
  useWorkout: vi.fn(() => ({
    workoutSession: {
      workoutId: '123',
      startTime: '2024-01-01T10:00:00Z',
      endTime: '2024-01-01T11:00:00Z',
      exercises: [
        {
          id: '1',
          name: 'Bench Press',
          sets: [
            {
              id: '1',
              reps: 10,
              weight: { Lb: 135, Kg: 61 },
              isWarmup: false,
              rir: 2,
            },
            {
              id: '2',
              reps: 8,
              weight: { Lb: 155, Kg: 70 },
              isWarmup: false,
              rir: 1,
            },
          ],
        },
      ],
    },
    finishWorkout: vi.fn(),
    isLoading: false,
    error: null,
  })),
}))

vi.mock('@/stores/userStatsStore', () => ({
  useUserStatsStore: vi.fn(() => ({
    stats: {
      weekStreak: 5,
      workoutsCompleted: 42,
      lbsLifted: 125000,
    },
    isLoading: false,
    error: null,
    fetchStats: vi.fn(),
  })),
}))

describe('WorkoutComplete - Share Functionality', () => {
  beforeEach(() => {
    shareButtonProps = null
  })

  it('should display share button after stats', () => {
    render(<WorkoutComplete />)

    const shareButton = screen.getByRole('button', { name: /share$/i })
    expect(shareButton).toBeInTheDocument()
  })

  it('should have proper touch target size', () => {
    render(<WorkoutComplete />)

    const shareButton = screen.getByRole('button', { name: /share$/i })
    expect(shareButton).toBeInTheDocument()

    // Check that ShareButton receives proper className for min-height
    expect(shareButtonProps.className).toContain('min-h-[44px]')
  })

  it('should include dr-muscle.com link in share message', () => {
    render(<WorkoutComplete />)

    // Verify the ShareButton was called with the correct shareData
    expect(shareButtonProps).toBeDefined()
    expect(shareButtonProps.shareData).toBeDefined()
    expect(shareButtonProps.shareData.text).toContain('https://dr-muscle.com')
  })

  it('should include workout stats in share message', () => {
    render(<WorkoutComplete />)

    expect(shareButtonProps.shareData.text).toContain('1 exercises')
    expect(shareButtonProps.shareData.text).toContain('2 sets')
    expect(shareButtonProps.shareData.text).toContain('2,590 lbs lifted')
    expect(shareButtonProps.shareData.text).toContain('5 weeks streak')
  })

  it('should handle singular week correctly in share message', () => {
    vi.mocked(useUserStatsStore).mockReturnValue({
      stats: {
        weekStreak: 1,
        workoutsCompleted: 42,
        lbsLifted: 125000,
      },
      isLoading: false,
      error: null,
      fetchStats: vi.fn(),
    } as any)

    render(<WorkoutComplete />)

    expect(shareButtonProps.shareData.text).toContain('1 week streak')
    expect(shareButtonProps.shareData.text).not.toContain('1 weeks streak')
  })

  it('should use appropriate share styling', () => {
    render(<WorkoutComplete />)

    expect(shareButtonProps.className).toContain('flex items-center gap-3')
    expect(shareButtonProps.className).toContain('bg-bg-secondary')
    expect(shareButtonProps.className).toContain('hover:bg-bg-tertiary')
    expect(shareButtonProps.showLabel).toBe(true)
  })
})
