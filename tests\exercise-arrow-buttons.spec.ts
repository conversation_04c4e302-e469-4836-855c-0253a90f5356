import { test, expect } from '@playwright/test'
import { loginUser } from './utils/auth-helpers'

test.describe('Exercise Arrow Buttons', () => {
  test.beforeEach(async ({ page }) => {
    await loginUser(page)
  })

  test('should update reps and weight using arrow buttons', async ({
    page,
  }) => {
    // Navigate to workout page
    await page.goto('/workout')

    // Click on the first exercise
    await page.locator('[data-testid="exercise-card"]').first().click()

    // Wait for the exercise page to load
    await page.waitForSelector('[aria-label="Reps for set 1"]')

    // Get initial values
    const repsInput = page.locator('[aria-label="Reps for set 1"]')
    const weightInput = page.locator('[aria-label="Weight for set 1"]')

    const initialReps = await repsInput.inputValue()
    const initialWeight = await weightInput.inputValue()

    // Click the up arrow for reps
    await page.locator('[aria-label="Increase reps"]').click()

    // Verify reps increased by 1
    const newReps = await repsInput.inputValue()
    expect(Number(newReps)).toBe(Number(initialReps) + 1)

    // Click the down arrow for weight
    await page.locator('[aria-label="Decrease weight"]').click()

    // Verify weight decreased by 2.5 (assuming lbs)
    const newWeight = await weightInput.inputValue()
    expect(Number(newWeight)).toBe(Number(initialWeight) - 2.5)

    // Test boundary: reps should not go below 1
    // Set reps to 1
    await repsInput.fill('1')

    // Click down arrow for reps
    await page.locator('[aria-label="Decrease reps"]').click()

    // Verify reps stayed at 1
    const finalReps = await repsInput.inputValue()
    expect(Number(finalReps)).toBe(1)
  })

  test('should only show arrows for active set', async ({ page }) => {
    // Navigate to workout page
    await page.goto('/workout')

    // Click on the first exercise
    await page.locator('[data-testid="exercise-card"]').first().click()

    // Wait for the exercise page to load
    await page.waitForSelector('[aria-label="Reps for set 1"]')

    // Verify arrows are visible for the active set
    await expect(page.locator('[aria-label="Increase reps"]')).toBeVisible()
    await expect(page.locator('[aria-label="Decrease reps"]')).toBeVisible()
    await expect(page.locator('[aria-label="Increase weight"]')).toBeVisible()
    await expect(page.locator('[aria-label="Decrease weight"]')).toBeVisible()

    // Complete the first set by clicking save
    await page.locator('button:has-text("Save")').click()

    // Wait for set to be marked as complete
    await page.waitForTimeout(1000)

    // Verify arrows moved to the next active set
    // The first set should no longer have arrows
    const firstSetContainer = page
      .locator('[aria-label="Reps for set 1"]')
      .locator('..')
    await expect(
      firstSetContainer.locator('[aria-label="Increase reps"]')
    ).not.toBeVisible()
  })
})
