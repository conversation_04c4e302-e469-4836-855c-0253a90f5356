'use client'

import React from 'react'
import type {
  ExerciseModel,
  WorkoutLogSerieModel,
  RecommendationModel,
} from '@/types'
import type { WorkoutLogSerieModelRef } from '@/types/api/WorkoutLogSerieModelRef'
import { SetCell } from './SetCell'
import { ExplainerBox } from './ExplainerBox'
import { createWorkoutSets } from '@/utils/createWorkoutSetsMAUI'
import { getSetTypeFromSet } from '@/utils/getSetTypeFromSet'

interface ExerciseSetsGridProps {
  exercise: ExerciseModel
  recommendation?: RecommendationModel
  sets?: (WorkoutLogSerieModel & Partial<WorkoutLogSerieModelRef>)[]
  userBodyWeight?: number
  onSetUpdate: (
    setId: number,
    updates: { reps?: number; weight?: number }
  ) => void
  onSetComplete?: (setIndex: number) => void
  onFinishExercise?: () => void
  onAddSet?: () => void
  onOneRMUpdate?: (data: {
    weight: number
    reps: number
    exercise?: {
      Id?: number
      Name?: string
      IsBodyweight?: boolean
    }
    recommendation?: {
      ExerciseId?: number
      Weight?: { Lb: number; Kg: number }
      Reps?: number
      Series?: number
    }
    isKg: boolean
    userBodyWeight: number
    isFirstWorkSet?: boolean
  }) => void
  onSaveCurrentSet?: () => void
  isSaving?: boolean
  unit?: 'kg' | 'lbs'
}

export function ExerciseSetsGrid({
  exercise,
  recommendation,
  sets: providedSets,
  userBodyWeight = 80,
  onSetUpdate,
  onSetComplete,
  onFinishExercise,
  onAddSet,
  onOneRMUpdate,
  onSaveCurrentSet,
  isSaving = false,
  unit = 'lbs',
}: ExerciseSetsGridProps) {
  // Generate sets using MAUI logic if recommendation is provided
  const sets =
    providedSets ||
    (recommendation
      ? createWorkoutSets(exercise, recommendation, unit === 'kg')
      : [])

  // Check if all sets are finished
  const allSetsFinished = sets.length > 0 && sets.every((set) => set.IsFinished)

  // Check if any set has IsNext
  const currentSetIndex = sets.findIndex((set) => set.IsNext)

  if (sets.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[200px] p-4">
        <p className="text-text-secondary mb-4">No sets for this exercise</p>
        {onAddSet && (
          <button
            onClick={onAddSet}
            className="px-4 py-2 border-2 border-brand-primary text-brand-primary bg-transparent rounded-theme font-medium hover:bg-brand-primary hover:text-text-inverse transition-colors min-h-[44px]"
          >
            Add set
          </button>
        )}
      </div>
    )
  }

  return (
    <div className="flex flex-col">
      {/* Header Row */}
      <SetCell isHeaderCell unit={unit} />

      {/* Sets */}
      <div className="divide-y divide-border-secondary">
        {sets.map((set, index) => {
          const isWarmup = set.IsWarmups || false
          const warmupCount = recommendation?.WarmupsCount || 0

          // Calculate display number: 'W' for warmups, 1-based for work sets
          let displaySetNo: string | number
          if (isWarmup) {
            displaySetNo = 'W'
          } else {
            // Work sets start at 1 (subtract warmup count from index)
            displaySetNo = index - warmupCount + 1
          }

          const isLastSet = index === sets.length - 1
          let weight = 0
          if (set.Weight) {
            weight = unit === 'kg' ? set.Weight.Kg || 0 : set.Weight.Lb || 0
          }

          return (
            <div
              key={set.Id || index}
              className={
                currentSetIndex === index ? 'ring-2 ring-brand-primary' : ''
              }
            >
              <SetCell
                setNo={displaySetNo}
                reps={set.Reps}
                weight={weight}
                isFinished={set.IsFinished}
                isBodyweight={exercise.IsBodyweight}
                isLastSet={isLastSet && !allSetsFinished} // Don't show last set UI when all done
                isExerciseFinished={false} // Don't show finish button in individual cells
                isNext={set.IsNext || false}
                backColor={set.BackColor || 'transparent'}
                weightSingal={set.WeightSingal}
                setType={getSetTypeFromSet(set)}
                onRepsChange={(reps) =>
                  onSetUpdate(set.Id || index + 1, { reps })
                }
                onWeightChange={(weight) =>
                  onSetUpdate(set.Id || index + 1, { weight })
                }
                setData={set}
                exercise={{
                  Id: exercise.Id,
                  Name: exercise.Label,
                  IsBodyweight: exercise.IsBodyweight,
                }}
                recommendation={
                  recommendation
                    ? {
                        ExerciseId: recommendation.ExerciseId,
                        Weight: recommendation.Weight,
                        Reps: recommendation.Reps,
                        Series: recommendation.Series,
                        Increments: recommendation.Increments,
                      }
                    : undefined
                }
                userBodyWeight={userBodyWeight}
                onSetComplete={() => onSetComplete?.(index)}
                onFinishExercise={undefined} // Handle finish at grid level
                onAddSet={isLastSet && !allSetsFinished ? onAddSet : undefined}
                onOneRMUpdate={onOneRMUpdate}
                unit={unit}
              />

              {/* Show ExplainerBox and Save button under active set */}
              {set.IsNext && (
                <>
                  <ExplainerBox
                    recommendation={recommendation || null}
                    currentSetIndex={index}
                    isWarmup={set.IsWarmups || false}
                    unit={unit}
                    currentReps={set.Reps}
                    currentWeight={weight}
                    isFirstWorkSet={
                      !set.IsWarmups &&
                      index === (recommendation?.WarmupsCount || 0)
                    }
                  />
                  {onSaveCurrentSet && (
                    <div className="px-4 pb-4">
                      <button
                        onClick={onSaveCurrentSet}
                        disabled={isSaving}
                        data-testid="floating-save-button"
                        className={`w-full py-4 min-h-[56px] rounded-theme font-semibold text-lg transition-all ${
                          isSaving
                            ? 'bg-bg-tertiary text-text-tertiary cursor-not-allowed'
                            : 'bg-gradient-metallic-gold text-text-inverse shadow-theme-xl hover:shadow-theme-2xl active:scale-[0.98] shimmer-hover text-shadow-sm'
                        }`}
                      >
                        {isSaving ? 'Saving...' : 'Save set'}
                      </button>
                    </div>
                  )}
                </>
              )}
            </div>
          )
        })}
      </div>

      {/* All sets done message (shown once, not per set) */}
      {allSetsFinished && (
        <div className="px-4 py-6">
          <div className="bg-success/10 rounded-theme p-4">
            <p className="text-center text-success font-medium italic">
              All sets done—congrats!
            </p>
          </div>
          {onFinishExercise && (
            <button
              onClick={onFinishExercise}
              className="w-full mt-4 bg-success text-text-inverse font-bold py-4 rounded-theme text-lg hover:bg-success/90 transition-colors min-h-[66px]"
            >
              Finish exercise
            </button>
          )}
        </div>
      )}
    </div>
  )
}
