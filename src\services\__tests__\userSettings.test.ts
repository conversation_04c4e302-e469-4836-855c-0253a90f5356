/**
 * Tests for user settings service
 */

import {
  getUserSettings,
  getUserSettingsSync,
  shouldUseRestPause,
  shouldUseRestPauseSync,
} from '../userSettings'
import { userProfileApi } from '@/api/userProfile'

// Mock the userProfileApi
vi.mock('@/api/userProfile', () => ({
  userProfileApi: {
    getUserInfo: vi.fn(),
  },
}))

// Mock logger
vi.mock('@/utils/logger', () => ({
  logger: {
    debug: vi.fn(),
    error: vi.fn(),
  },
}))

describe('UserSettings Service', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // localStorage is already mocked in setup.ts, just reset the mocks
    vi.mocked(localStorage.getItem).mockReset()
    vi.mocked(localStorage.setItem).mockReset()
    vi.mocked(localStorage.clear).mockReset()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('getUserSettings', () => {
    it('should return default settings when API call succeeds', async () => {
      // Mock successful API response
      vi.mocked(userProfileApi.getUserInfo).mockResolvedValue({
        StatusCode: 200,
        Result: {
          Email: '<EMAIL>',
          FirstName: 'Test',
          LastName: 'User',
        },
      })

      // Mock localStorage to return null for all keys
      vi.mocked(localStorage.getItem).mockReturnValue(null)

      const settings = await getUserSettings()

      expect(settings).toEqual({
        isQuickMode: null, // Updated to match mobile app behavior
        isStrengthPhase: false,
        isFreePlan: false,
        isFirstWorkoutOfStrengthPhase: false,
        lightSessionDays: null,
        setStyle: undefined,
        isPyramid: false,
      })
    })

    it('should return default settings when API call fails', async () => {
      // Mock API failure
      vi.mocked(userProfileApi.getUserInfo).mockRejectedValue(
        new Error('API Error')
      )

      // Mock localStorage to return null for all keys
      vi.mocked(localStorage.getItem).mockReturnValue(null)

      const settings = await getUserSettings()

      expect(settings).toEqual({
        isQuickMode: null, // Updated to match mobile app behavior
        isStrengthPhase: false,
        isFreePlan: false,
        isFirstWorkoutOfStrengthPhase: false,
        lightSessionDays: null,
        setStyle: undefined,
        isPyramid: false,
      })
    })
  })

  describe('getUserSettingsSync', () => {
    it('should return default settings synchronously', () => {
      // Mock localStorage to return null for all keys
      vi.mocked(localStorage.getItem).mockReturnValue(null)

      const settings = getUserSettingsSync()

      expect(settings).toEqual({
        isQuickMode: null, // Updated to match mobile app behavior
        isStrengthPhase: false,
        isFreePlan: false,
        isFirstWorkoutOfStrengthPhase: false,
        lightSessionDays: null,
        setStyle: undefined,
        isPyramid: false,
      })
    })
  })

  describe('shouldUseRestPause', () => {
    beforeEach(() => {
      // Reset mocks before each test
      vi.mocked(localStorage.getItem).mockReset()
      vi.mocked(userProfileApi.getUserInfo).mockReset()
    })

    it('should return true when user SetStyle preference is RestPause', async () => {
      // Mock localStorage to return RestPause preference
      vi.mocked(localStorage.getItem).mockImplementation((key) => {
        if (key === 'SetStyle') return 'RestPause'
        if (key === 'IsPyramid') return 'false'
        return null
      })

      // Mock successful API response
      vi.mocked(userProfileApi.getUserInfo).mockResolvedValue({
        StatusCode: 200,
        Result: {
          Email: '<EMAIL>',
          FirstName: 'Test',
          LastName: 'User',
        },
      })

      const result = await shouldUseRestPause({ IsBodyweight: false })

      expect(result).toBe(true)
    })

    it('should return true when isPyramid is true and exercise is bodyweight', async () => {
      // Mock localStorage with pyramid preference
      vi.mocked(localStorage.getItem).mockImplementation((key) => {
        if (key === 'SetStyle') return 'Normal'
        if (key === 'IsPyramid') return 'true'
        return null
      })

      // Mock successful API response
      vi.mocked(userProfileApi.getUserInfo).mockResolvedValue({
        StatusCode: 200,
        Result: {
          Email: '<EMAIL>',
          FirstName: 'Test',
          LastName: 'User',
        },
      })

      const result = await shouldUseRestPause({ IsBodyweight: true })

      expect(result).toBe(true)
    })

    it('should return false when neither condition is met', async () => {
      // Mock localStorage with normal preferences
      vi.mocked(localStorage.getItem).mockImplementation((key) => {
        if (key === 'SetStyle') return 'Normal'
        if (key === 'IsPyramid') return 'false'
        return null
      })

      // Mock successful API response
      vi.mocked(userProfileApi.getUserInfo).mockResolvedValue({
        StatusCode: 200,
        Result: {
          Email: '<EMAIL>',
          FirstName: 'Test',
          LastName: 'User',
        },
      })

      const result = await shouldUseRestPause({ IsBodyweight: false })

      expect(result).toBe(false)
    })

    it('should return false when pyramid is true but exercise is not bodyweight', async () => {
      // Mock localStorage with pyramid preference
      vi.mocked(localStorage.getItem).mockImplementation((key) => {
        if (key === 'SetStyle') return 'Normal'
        if (key === 'IsPyramid') return 'true'
        return null
      })

      // Mock successful API response
      vi.mocked(userProfileApi.getUserInfo).mockResolvedValue({
        StatusCode: 200,
        Result: {
          Email: '<EMAIL>',
          FirstName: 'Test',
          LastName: 'User',
        },
      })

      const result = await shouldUseRestPause({ IsBodyweight: false })

      expect(result).toBe(false)
    })

    it('should handle API errors gracefully', async () => {
      // Mock API failure
      vi.mocked(userProfileApi.getUserInfo).mockRejectedValue(
        new Error('API Error')
      )

      const result = await shouldUseRestPause({ IsBodyweight: true })

      expect(result).toBe(false)
    })
  })

  describe('shouldUseRestPauseSync', () => {
    beforeEach(() => {
      // Reset mocks before each test
      vi.mocked(localStorage.getItem).mockReset()
    })

    it('should return true when user SetStyle preference is RestPause', () => {
      // Mock localStorage with RestPause preference
      vi.mocked(localStorage.getItem).mockImplementation((key) => {
        if (key === 'SetStyle') return 'RestPause'
        if (key === 'IsPyramid') return 'false'
        return null
      })

      const result = shouldUseRestPauseSync({ IsBodyweight: false })

      expect(result).toBe(true)
    })

    it('should return true when isPyramid is true and exercise is bodyweight', () => {
      // Mock localStorage with pyramid preference
      vi.mocked(localStorage.getItem).mockImplementation((key) => {
        if (key === 'SetStyle') return 'Normal'
        if (key === 'IsPyramid') return 'true'
        return null
      })

      const result = shouldUseRestPauseSync({ IsBodyweight: true })

      expect(result).toBe(true)
    })

    it('should return false when neither condition is met', () => {
      // Mock localStorage with normal preferences
      vi.mocked(localStorage.getItem).mockImplementation((key) => {
        if (key === 'SetStyle') return 'Normal'
        if (key === 'IsPyramid') return 'false'
        return null
      })

      const result = shouldUseRestPauseSync({ IsBodyweight: false })

      expect(result).toBe(false)
    })

    it('should return false when pyramid is true but exercise is not bodyweight', () => {
      // Mock localStorage with pyramid preference
      vi.mocked(localStorage.getItem).mockImplementation((key) => {
        if (key === 'SetStyle') return 'Normal'
        if (key === 'IsPyramid') return 'true'
        return null
      })

      const result = shouldUseRestPauseSync({ IsBodyweight: false })

      expect(result).toBe(false)
    })

    it('should handle empty exercise object', () => {
      // Mock localStorage
      vi.mocked(localStorage.getItem).mockImplementation((key) => {
        if (key === 'SetStyle') return 'Normal'
        if (key === 'IsPyramid') return 'true'
        return null
      })

      const result = shouldUseRestPauseSync({}) // Exercise without IsBodyweight property

      expect(result).toBe(false)
    })
  })
})
