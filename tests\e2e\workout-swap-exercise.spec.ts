import { test, expect } from '@playwright/test'
import { setupAuth } from './helpers/auth-helper'
import {
  mockWorkoutAPIs,
  mockRecommendations,
  mockSetLogUpdate,
  mockFinishWorkout,
} from './helpers/workout-api-mocks'
import { mockProgram } from './mocks/program'

test.describe('Exercise Swap Feature', () => {
  test.beforeEach(async ({ page }) => {
    // Setup auth and mocks
    await setupAuth(page)
    await mockWorkoutAPIs(page, mockProgram)
    await mockRecommendations(page)
    await mockSetLogUpdate(page)
    await mockFinishWorkout(page)

    // Navigate to workout page
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="workout-overview-container"]')
  })

  test('should not allow swap when workout is not started', async ({
    page,
  }) => {
    // Find first exercise card
    const exerciseCard = page.locator('[data-testid="exercise-card"]').first()
    await exerciseCard.waitFor()

    // Click swap button
    const swapButton = exerciseCard.locator('[aria-label="Swap exercise"]')
    await swapButton.click()

    // Verify modal is not shown (because workout hasn't started)
    await expect(
      page.locator('[data-testid="exercise-swap-modal"]')
    ).not.toBeVisible()
  })

  test('should allow swap after workout is started', async ({ page }) => {
    // Mock exercise search API
    await page.route('**/api/Exercise/GetByBodyPart/*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Result: [
            {
              Id: 102,
              Label: 'Incline Bench Press',
              IsSystemExercise: true,
              IsSwapTarget: false,
              IsFinished: false,
              BodyPartId: 1,
              IsUnilateral: false,
              IsTimeBased: false,
              EquipmentId: 2,
              IsEasy: false,
              IsMedium: false,
              IsBodyweight: false,
              VideoUrl: '',
              IsNextExercise: false,
              IsPlate: true,
              IsWeighted: true,
              IsPyramid: false,
              RepsMaxValue: 10,
              RepsMinValue: 6,
              Timer: undefined,
              IsNormalSets: true,
              WorkoutGroupId: undefined,
              IsBodypartPriority: false,
              IsFlexibility: false,
              IsOneHanded: false,
              LocalVideo: '',
              IsAssisted: false,
              SetStyle: 'Normal',
            },
            {
              Id: 103,
              Label: 'Push-ups',
              IsSystemExercise: true,
              IsSwapTarget: false,
              IsFinished: false,
              BodyPartId: 1,
              IsUnilateral: false,
              IsTimeBased: false,
              EquipmentId: 0,
              IsEasy: true,
              IsMedium: false,
              IsBodyweight: true,
              VideoUrl: '',
              IsNextExercise: false,
              IsPlate: false,
              IsWeighted: false,
              IsPyramid: false,
              RepsMaxValue: 20,
              RepsMinValue: 10,
              Timer: undefined,
              IsNormalSets: true,
              WorkoutGroupId: undefined,
              IsBodypartPriority: false,
              IsFlexibility: false,
              IsOneHanded: false,
              LocalVideo: '',
              IsAssisted: false,
              SetStyle: 'Normal',
            },
          ],
        }),
      })
    })

    // Start workout
    const startButton = page.locator(
      '[aria-label="Start a new workout session"]'
    )
    await startButton.click()

    // Wait for navigation to first exercise
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Go back to workout overview
    await page.goBack()
    await page.waitForSelector('[data-testid="workout-overview-container"]')

    // Find first exercise card
    const exerciseCard = page.locator('[data-testid="exercise-card"]').first()
    await exerciseCard.waitFor()

    // Click swap button
    const swapButton = exerciseCard.locator('[aria-label="Swap exercise"]')
    await swapButton.click()

    // Verify modal is shown
    await expect(
      page.locator('[role="dialog"][aria-label="Swap Exercise"]')
    ).toBeVisible()

    // Verify alternative exercises are loaded
    await expect(page.locator('text=Incline Bench Press')).toBeVisible()
    await expect(page.locator('text=Push-ups')).toBeVisible()

    // Select an alternative exercise
    await page.locator('text=Push-ups').click()

    // Choose temporary swap
    await page.locator('text=Swap for this workout only').click()

    // Click swap button in modal
    await page.locator('button:has-text("Swap Exercise")').click()

    // Verify modal is closed
    await expect(
      page.locator('[role="dialog"][aria-label="Swap Exercise"]')
    ).not.toBeVisible()
  })

  test('should handle swap error gracefully', async ({ page }) => {
    // Mock exercise search API to return error
    await page.route('**/api/Exercise/GetByBodyPart/*', async (route) => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Server error' }),
      })
    })

    // Start workout
    const startButton = page.locator(
      '[aria-label="Start a new workout session"]'
    )
    await startButton.click()

    // Wait for navigation to first exercise
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Go back to workout overview
    await page.goBack()
    await page.waitForSelector('[data-testid="workout-overview-container"]')

    // Find first exercise card
    const exerciseCard = page.locator('[data-testid="exercise-card"]').first()
    await exerciseCard.waitFor()

    // Click swap button
    const swapButton = exerciseCard.locator('[aria-label="Swap exercise"]')
    await swapButton.click()

    // Verify modal is shown
    await expect(
      page.locator('[role="dialog"][aria-label="Swap Exercise"]')
    ).toBeVisible()

    // Verify error state
    await expect(
      page.locator('text=Failed to load alternative exercises')
    ).toBeVisible()

    // Close modal
    await page.locator('button:has-text("Cancel")').click()
    await expect(
      page.locator('[role="dialog"][aria-label="Swap Exercise"]')
    ).not.toBeVisible()
  })
})
