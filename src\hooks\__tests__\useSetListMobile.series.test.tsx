import { describe, it, expect } from 'vitest'
import { renderHook } from '@testing-library/react'
import { useSetListMobile } from '../useSetListMobile'
import type { ExerciseModel, RecommendationModel } from '@/types/api'

describe('useSetListMobile - Series handling', () => {
  const mockExercise: ExerciseModel = {
    Id: 1,
    Label: 'Bench Press',
    IsBodyweight: false,
    IsTimeBased: false,
    IsUnilateral: false,
    IsFlexibility: false,
    VideoUrl: '',
    BodyPartId: 1,
  }

  it('should generate the correct number of work sets when Series is defined', () => {
    const recommendation: RecommendationModel = {
      Series: 5,
      Reps: 10,
      Weight: { Kg: 60, Lb: 132 },
      WarmUpsList: [],
    } as RecommendationModel

    const { result } = renderHook(() =>
      useSetListMobile(mockExercise, recommendation, 'kg')
    )

    expect(result.current.exerciseWorkSets?.Sets.length).toBe(5)
    expect(
      result.current.exerciseWorkSets?.Sets.filter((s) => !s.IsWarmups).length
    ).toBe(5)
  })

  it('should not default to 3 sets when Series is 0', () => {
    const recommendation: RecommendationModel = {
      Series: 0,
      Reps: 10,
      Weight: { Kg: 60, Lb: 132 },
      WarmUpsList: [],
    } as RecommendationModel

    const { result } = renderHook(() =>
      useSetListMobile(mockExercise, recommendation, 'kg')
    )

    expect(result.current.exerciseWorkSets?.Sets.length).toBe(0)
  })

  it('should generate warmup sets correctly', () => {
    const recommendation: RecommendationModel = {
      Series: 2,
      Reps: 10,
      Weight: { Kg: 60, Lb: 132 },
      WarmUpsList: [
        { WarmUpReps: 5, WarmUpWeightSet: { Kg: 20, Lb: 44 } },
        { WarmUpReps: 5, WarmUpWeightSet: { Kg: 40, Lb: 88 } },
        { WarmUpReps: 3, WarmUpWeightSet: { Kg: 50, Lb: 110 } },
      ],
    } as RecommendationModel

    const { result } = renderHook(() =>
      useSetListMobile(mockExercise, recommendation, 'kg')
    )

    const warmupSets =
      result.current.exerciseWorkSets?.Sets.filter((s) => s.IsWarmups) || []
    const workSets =
      result.current.exerciseWorkSets?.Sets.filter((s) => !s.IsWarmups) || []

    expect(warmupSets.length).toBe(3)
    expect(workSets.length).toBe(2)
    expect(result.current.exerciseWorkSets?.Sets.length).toBe(5)

    // Check warmup set values
    expect(warmupSets[0].Reps).toBe(5)
    expect(warmupSets[0].Weight?.Kg).toBe(20)
    expect(warmupSets[1].Reps).toBe(5)
    expect(warmupSets[1].Weight?.Kg).toBe(40)
    expect(warmupSets[2].Reps).toBe(3)
    expect(warmupSets[2].Weight?.Kg).toBe(50)
  })

  it('should handle undefined Series by defaulting to 0', () => {
    const recommendation: RecommendationModel = {
      Reps: 10,
      Weight: { Kg: 60, Lb: 132 },
      WarmUpsList: [],
      // Series is undefined
    } as RecommendationModel

    const { result } = renderHook(() =>
      useSetListMobile(mockExercise, recommendation, 'kg')
    )

    // Should not generate any work sets if Series is undefined
    const workSets =
      result.current.exerciseWorkSets?.Sets.filter((s) => !s.IsWarmups) || []
    expect(workSets.length).toBe(0)
  })

  it('should handle pyramid sets correctly', () => {
    const recommendation: RecommendationModel = {
      Series: 4,
      Reps: 10,
      Weight: { Kg: 60, Lb: 132 },
      IsPyramid: true,
      WarmUpsList: [],
    } as RecommendationModel

    const { result } = renderHook(() =>
      useSetListMobile(mockExercise, recommendation, 'kg')
    )

    const workSets =
      result.current.exerciseWorkSets?.Sets.filter((s) => !s.IsWarmups) || []
    expect(workSets.length).toBe(4)
  })
})
