/**
 * Tests for workout template modification API
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import {
  replaceExerciseInWorkout,
  addExerciseToWorkout,
  removeExerciseFromWorkout,
} from '../template'
import { apiClient } from '../../client'
import type { WorkoutTemplateModel } from '@/types'

// Mock the API client
vi.mock('../../client', () => ({
  apiClient: {
    get: vi.fn(),
    post: vi.fn(),
  },
}))

// Mock auth utils
vi.mock('@/lib/auth-utils', () => ({
  getCurrentUserEmail: vi.fn(() => '<EMAIL>'),
}))

describe('Workout Template API', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('replaceExerciseInWorkout', () => {
    const mockWorkout: WorkoutTemplateModel = {
      Id: 100,
      UserId: 'test-user',
      Label: 'Test Workout',
      Exercises: [
        {
          Id: 1,
          Label: 'Exercise 1',
          BodyPartId: 1,
          IsSystemExercise: true,
          IsSwapTarget: false,
          IsFinished: false,
          IsUnilateral: false,
          IsTimeBased: false,
          EquipmentId: 1,
          IsEasy: false,
          IsMedium: false,
          IsBodyweight: false,
          VideoUrl: '',
          IsNextExercise: false,
          IsPlate: false,
          IsWeighted: true,
          IsPyramid: false,
          RepsMaxValue: 12,
          RepsMinValue: 8,
          Timer: undefined,
          IsNormalSets: true,
          WorkoutGroupId: undefined,
          IsBodypartPriority: false,
          IsFlexibility: false,
          IsOneHanded: false,
          LocalVideo: '',
          IsAssisted: false,
          SetStyle: 'Normal',
        },
        {
          Id: 2,
          Label: 'Exercise 2',
          BodyPartId: 2,
          IsSystemExercise: true,
          IsSwapTarget: false,
          IsFinished: false,
          IsUnilateral: false,
          IsTimeBased: false,
          EquipmentId: 2,
          IsEasy: false,
          IsMedium: false,
          IsBodyweight: false,
          VideoUrl: '',
          IsNextExercise: false,
          IsPlate: false,
          IsWeighted: true,
          IsPyramid: false,
          RepsMaxValue: 12,
          RepsMinValue: 8,
          Timer: undefined,
          IsNormalSets: true,
          WorkoutGroupId: undefined,
          IsBodypartPriority: false,
          IsFlexibility: false,
          IsOneHanded: false,
          LocalVideo: '',
          IsAssisted: false,
          SetStyle: 'Normal',
        },
      ],
      IsSystemExercise: true,
      WorkoutSettingsModel: {},
    }

    it('should use compound endpoint when available', async () => {
      // Mock getting workout details
      vi.mocked(apiClient.get).mockResolvedValueOnce({
        data: { Result: mockWorkout },
      })

      // Mock successful compound replace
      vi.mocked(apiClient.post).mockResolvedValueOnce({
        data: { Result: true, Code: 200 },
      })

      const result = await replaceExerciseInWorkout(100, 1, 3)

      expect(apiClient.get).toHaveBeenCalledWith(
        '/api/Workout/GetUserCustomizedCurrentWorkout/100'
      )
      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/WorkoutTemplate/ReplaceExercise',
        {
          WorkoutId: 100,
          OldExerciseId: 1,
          NewExerciseId: 3,
          Position: 0, // First exercise position
          Username: '<EMAIL>',
        }
      )

      expect(result).toEqual({
        Result: true,
        Code: 200,
        ErrorMessage: null,
      })
    })

    it('should fall back to sequential operations when compound endpoint fails', async () => {
      // Mock getting workout details
      vi.mocked(apiClient.get).mockResolvedValueOnce({
        data: { Result: mockWorkout },
      })

      // Mock compound endpoint failure, then successful sequential operations
      vi.mocked(apiClient.post)
        .mockRejectedValueOnce(new Error('Compound endpoint not found'))
        .mockResolvedValueOnce({ data: { Result: true, Code: 200 } }) // Remove
        .mockResolvedValueOnce({ data: { Result: true, Code: 200 } }) // Add

      const result = await replaceExerciseInWorkout(100, 2, 4)

      expect(apiClient.post).toHaveBeenCalledTimes(3)

      // Should try compound first
      expect(apiClient.post).toHaveBeenNthCalledWith(
        1,
        '/api/WorkoutTemplate/ReplaceExercise',
        expect.any(Object)
      )

      // Then fall back to remove
      expect(apiClient.post).toHaveBeenNthCalledWith(
        2,
        '/api/WorkoutTemplate/RemoveExercise',
        {
          WorkoutId: 100,
          ExerciseId: 2,
          Username: '<EMAIL>',
        }
      )

      // Then add
      expect(apiClient.post).toHaveBeenNthCalledWith(
        3,
        '/api/WorkoutTemplate/AddExercise',
        {
          WorkoutId: 100,
          ExerciseId: 4,
          Position: 1, // Second exercise position
          Username: '<EMAIL>',
        }
      )

      expect(result).toEqual({
        Result: true,
        Code: 200,
        ErrorMessage: null,
      })
    })

    it('should handle exercise not found error', async () => {
      // Mock getting workout details
      vi.mocked(apiClient.get).mockResolvedValueOnce({
        data: { Result: mockWorkout },
      })

      await expect(replaceExerciseInWorkout(100, 999, 3)).rejects.toThrow(
        'Exercise 999 not found in workout'
      )
    })
  })

  describe('addExerciseToWorkout', () => {
    it('should call add exercise endpoint', async () => {
      vi.mocked(apiClient.post).mockResolvedValueOnce({
        data: { Result: true, Code: 200 },
      })

      const result = await addExerciseToWorkout(100, 5, 2)

      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/WorkoutTemplate/AddExercise',
        {
          WorkoutId: 100,
          ExerciseId: 5,
          Position: 2,
          Username: '<EMAIL>',
        }
      )

      expect(result).toEqual({
        Result: true,
        Code: 200,
        ErrorMessage: undefined,
      })
    })
  })

  describe('removeExerciseFromWorkout', () => {
    it('should call remove exercise endpoint', async () => {
      vi.mocked(apiClient.post).mockResolvedValueOnce({
        data: { Result: true, Code: 200 },
      })

      const result = await removeExerciseFromWorkout(100, 2)

      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/WorkoutTemplate/RemoveExercise',
        {
          WorkoutId: 100,
          ExerciseId: 2,
          Username: '<EMAIL>',
        }
      )

      expect(result).toEqual({
        Result: true,
        Code: 200,
        ErrorMessage: undefined,
      })
    })
  })
})
