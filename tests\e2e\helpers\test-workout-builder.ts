import type { WorkoutTemplateGroupModel, ExerciseModel } from '@/types'

export class TestWorkoutBuilder {
  private workout: WorkoutTemplateGroupModel

  constructor() {
    this.workout = {
      Id: 1,
      Label: 'Test Workout',
      IsFeaturedProgram: false,
      UserId: 'test-user-123',
      IsSystemExercise: false,
      RequiredWorkoutToLevelUp: 4,
      ProgramId: 1,
      WorkoutTemplates: [
        {
          Id: 101,
          Label: 'Test Day',
          UserId: 'test-user-123',
          IsSystemExercise: false,
          WorkoutSettingsModel: {},
          Exercices: [],
        },
      ],
    }
  }

  addExercise(
    exercise: Partial<ExerciseModel> & { Id: number; Label: string }
  ) {
    const fullExercise: ExerciseModel = {
      BodyPartId: 1,
      IsFinished: false,
      IsNextExercise: this.workout.WorkoutTemplates[0].Exercices.length === 0,
      IsSystemExercise: true,
      IsSwapTarget: false,
      IsUnilateral: false,
      IsTimeBased: false,
      IsEasy: false,
      IsMedium: true,
      IsBodyweight: false,
      VideoUrl: '',
      IsPlate: false,
      IsWeighted: true,
      IsPyramid: false,
      IsNormalSets: true,
      IsBodypartPriority: false,
      IsFlexibility: false,
      IsOneHanded: false,
      LocalVideo: '',
      IsAssisted: false,
      ...exercise,
    }

    this.workout.WorkoutTemplates[0].Exercices.push(fullExercise)
    return this
  }

  setWorkoutLabel(label: string) {
    this.workout.Label = label
    return this
  }

  setWorkoutTemplateLabel(label: string) {
    this.workout.WorkoutTemplates[0].Label = label
    return this
  }

  build(): WorkoutTemplateGroupModel {
    return this.workout
  }
}
