/**
 * Enhanced Service for managing exercise swaps
 * Handles both temporary (local) and permanent (database) swaps
 */

import { replaceExerciseInWorkout } from '@/api/workouts/template'
import { apiClient } from '@/api/client'
import { getCurrentUserEmail } from '@/lib/auth-utils'
import type { ExerciseModel, WorkoutTemplateModel } from '@/types'
import type { ExerciseSwapContext } from '@/stores/workoutStore/types'
import { logger } from '@/utils/logger'

export interface SwapExerciseOptions {
  temporary: boolean // Save locally only
  permanent: boolean // Save to database
}

export interface ExerciseSwapResult {
  workoutId: number
  sourceExercise: ExerciseModel
  targetExercise: ExerciseModel
  position: number // Index in exercise list
  savedToDb: boolean
}

export class SwapExerciseService {
  private static instance: SwapExerciseService | null = null

  private readonly TEMP_STORAGE_KEY = 'temp_swap_exercise_contexts'

  static getInstance(): SwapExerciseService {
    if (!SwapExerciseService.instance) {
      SwapExerciseService.instance = new SwapExerciseService()
    }
    return SwapExerciseService.instance
  }

  /**
   * Enhanced swap method that supports both temporary and permanent swaps
   */
  async swapExercise(
    workout: WorkoutTemplateModel,
    sourceExerciseId: number,
    targetExercise: ExerciseModel,
    options: SwapExerciseOptions
  ): Promise<ExerciseSwapResult> {
    // Find the position of the source exercise
    const position =
      workout.Exercises?.findIndex((e) => e.Id === sourceExerciseId) ?? -1
    if (position === -1 || !workout.Exercises) {
      throw new Error('Source exercise not found in workout')
    }

    const sourceExercise = workout.Exercises[position]
    if (!sourceExercise) {
      throw new Error('Source exercise not found at position')
    }

    if (options.temporary) {
      // Save to local storage (existing functionality)
      this.saveTemporarySwap(
        workout.Id,
        sourceExerciseId,
        targetExercise,
        sourceExercise
      )
    }

    if (options.permanent) {
      // Replace exercise in workout template and save to database
      await SwapExerciseService.savePermanentSwap(
        workout,
        position,
        targetExercise
      )
    }

    return {
      workoutId: workout.Id,
      sourceExercise,
      targetExercise,
      position,
      savedToDb: options.permanent,
    }
  }

  /**
   * Apply exercise swaps to a workout template
   * With the new template-based approach, this is mainly for local state management
   * The actual workout template on the server will be modified directly
   */
  static applySwapsToWorkout(
    workout: WorkoutTemplateModel,
    swaps: Record<number, ExerciseSwapContext>
  ): WorkoutTemplateModel {
    if (!workout.Exercises || Object.keys(swaps).length === 0) {
      return workout
    }

    const modifiedExercises = workout.Exercises.map((exercise) => {
      const swap = swaps[exercise.Id]

      if (swap && swap.targetExerciseId) {
        logger.log(
          `[SwapService] Applying local swap: ${exercise.Label} -> ${swap.label}`
        )

        return {
          ...exercise,
          Id: swap.targetExerciseId,
          Label: swap.label,
          BodyPartId: swap.bodyPartId,
          IsSwapTarget: true,
          IsSystemExercise: swap.isSystemExercise,
          // Preserve other original exercise properties
        }
      }

      return exercise
    })

    return {
      ...workout,
      Exercises: modifiedExercises,
    }
  }

  /**
   * Replace an exercise in a workout template array (for local state updates)
   * This maintains the same position in the exercises array
   */
  static replaceExerciseInArray(
    exercises: ExerciseModel[],
    oldExerciseId: number,
    newExercise: ExerciseModel
  ): ExerciseModel[] {
    return exercises.map((exercise) => {
      if (exercise.Id === oldExerciseId) {
        return {
          ...newExercise,
          IsSwapTarget: true,
        }
      }
      return exercise
    })
  }

  /**
   * Create a swap context from exercise data
   */
  static createSwapContext(
    workoutId: number,
    sourceExercise: ExerciseModel,
    targetExercise: ExerciseModel
  ): ExerciseSwapContext {
    return {
      workoutId,
      sourceExerciseId: sourceExercise.Id,
      sourceBodyPartId: sourceExercise.BodyPartId || 0,
      targetExerciseId: targetExercise.Id,
      bodyPartId: targetExercise.BodyPartId || 0,
      label: targetExercise.Label,
      isSystemExercise: targetExercise.IsSystemExercise || false,
      isSwapTarget: true,
      isFinished: false,
      swapDate: new Date().toISOString(),
      originalExercise: {
        label: sourceExercise.Label,
        bodyPartId: sourceExercise.BodyPartId || 0,
        isSystemExercise: sourceExercise.IsSystemExercise || false,
        equipmentId: sourceExercise.EquipmentId,
        setStyle: sourceExercise.SetStyle,
        isBodyweight: sourceExercise.IsBodyweight,
        isFlexibility: sourceExercise.IsFlexibility,
      },
    }
  }

  /**
   * Save permanent swap to database by modifying workout template
   */
  private static async savePermanentSwap(
    workout: WorkoutTemplateModel,
    position: number,
    newExercise: ExerciseModel
  ): Promise<void> {
    try {
      logger.log(
        `[SwapService] Saving permanent swap for workout ${workout.Id} at position ${position}`
      )

      // Create a copy of the workout with the exercise replaced
      const updatedWorkout: WorkoutTemplateModel = {
        ...workout,
        Exercises: [...(workout.Exercises || [])],
      }

      // Replace the exercise at the specified position
      const originalExercise = workout.Exercises![position]
      updatedWorkout.Exercises[position] = {
        ...newExercise,
        // Preserve any workout-specific properties from the original
        IsFinished: originalExercise?.IsFinished || false,
        IsNextExercise: originalExercise?.IsNextExercise || false,
      }

      // Call appropriate API endpoint based on workout type
      const username = getCurrentUserEmail()
      if (!username) {
        throw new Error('User not authenticated')
      }

      let result: { data?: { Result?: boolean; ErrorMessage?: string } }

      if (
        workout.IsSystemExercise ||
        workout.UserId === '89c52f09-240c-40a8-96df-9e8e152b7d63'
      ) {
        // System workout - create user copy
        logger.log(
          '[SwapService] Creating new user workout template for system workout'
        )
        result = await apiClient.post(
          '/api/Workout/CreateNewUserWorkoutTemplate',
          {
            ...updatedWorkout,
            Username: username,
          }
        )
      } else {
        // User workout - update directly
        logger.log('[SwapService] Updating existing user workout template')
        result = await apiClient.post('/api/Workout/CreateNewWorkoutTemplate', {
          ...updatedWorkout,
          Username: username,
        })
      }

      if (!result.data?.Result) {
        throw new Error(
          `Failed to save workout template: ${result.data?.ErrorMessage}`
        )
      }

      logger.log('[SwapService] Permanent swap saved successfully')
    } catch (error) {
      logger.error('[SwapService] Failed to save permanent swap:', error)
      throw error
    }
  }

  /**
   * Save temporary swap to local storage
   */
  private saveTemporarySwap(
    workoutId: number,
    sourceExerciseId: number,
    targetExercise: ExerciseModel,
    sourceExercise: ExerciseModel
  ): void {
    const swapContext: ExerciseSwapContext = {
      workoutId,
      sourceExerciseId,
      sourceBodyPartId: sourceExercise.BodyPartId || 0,
      targetExerciseId: targetExercise.Id,
      bodyPartId: targetExercise.BodyPartId || 0,
      label: targetExercise.Label,
      isSystemExercise: targetExercise.IsSystemExercise || false,
      isSwapTarget: true,
      isFinished: false,
      swapDate: new Date().toISOString(),
      originalExercise: {
        label: sourceExercise.Label,
        bodyPartId: sourceExercise.BodyPartId || 0,
        isSystemExercise: sourceExercise.IsSystemExercise || false,
        equipmentId: sourceExercise.EquipmentId,
        setStyle: sourceExercise.SetStyle,
        isBodyweight: sourceExercise.IsBodyweight,
        isFlexibility: sourceExercise.IsFlexibility,
      },
    }

    const swaps = this.getTemporarySwaps()
    const existingIndex = swaps.findIndex(
      (s) =>
        s.workoutId === workoutId && s.sourceExerciseId === sourceExerciseId
    )

    if (existingIndex >= 0) {
      swaps[existingIndex] = swapContext
    } else {
      swaps.push(swapContext)
    }

    localStorage.setItem(this.TEMP_STORAGE_KEY, JSON.stringify(swaps))
    logger.log('[SwapService] Temporary swap saved to local storage')
  }

  /**
   * Get temporary swaps from local storage
   */
  getTemporarySwaps(): ExerciseSwapContext[] {
    try {
      const data = localStorage.getItem(this.TEMP_STORAGE_KEY)
      return data ? JSON.parse(data) : []
    } catch (error) {
      logger.error('[SwapService] Failed to load temporary swaps:', error)
      return []
    }
  }

  /**
   * Clear temporary swap for a specific exercise
   */
  clearTemporarySwap(workoutId: number, sourceExerciseId: number): void {
    const swaps = this.getTemporarySwaps()
    const filtered = swaps.filter(
      (s) =>
        !(s.workoutId === workoutId && s.sourceExerciseId === sourceExerciseId)
    )
    localStorage.setItem(this.TEMP_STORAGE_KEY, JSON.stringify(filtered))
    logger.log('[SwapService] Temporary swap cleared')
  }

  /**
   * Execute exercise swap via API using workout template modification
   * This removes the old exercise and adds the new one in the same position
   * @deprecated Use the new swapExercise method instead
   */
  static async executeSwap(
    workoutId: number,
    sourceExerciseId: number,
    targetExerciseId: number
  ): Promise<boolean> {
    try {
      logger.log(
        `[SwapService] Executing legacy swap via template modification: ${sourceExerciseId} -> ${targetExerciseId} in workout ${workoutId}`
      )

      const result = await replaceExerciseInWorkout(
        workoutId,
        sourceExerciseId,
        targetExerciseId
      )

      if (result.Result) {
        logger.log('[SwapService] Legacy exercise replacement successful')
        return true
      } else {
        logger.error(
          '[SwapService] Legacy exercise replacement failed:',
          result.ErrorMessage
        )
        return false
      }
    } catch (error) {
      logger.error('[SwapService] Legacy exercise replacement error:', error)
      return false
    }
  }

  /**
   * Get the original exercise ID for a swapped exercise
   */
  static getOriginalExerciseId(
    exerciseId: number,
    swaps: Record<number, ExerciseSwapContext>
  ): number | null {
    // Find the swap where this exercise is the target
    const entry = Object.entries(swaps).find(
      ([, swap]) => swap.targetExerciseId === exerciseId
    )
    return entry ? parseInt(entry[0], 10) : null
  }

  /**
   * Check if an exercise is currently swapped
   */
  static isExerciseSwapped(
    exerciseId: number,
    swaps: Record<number, ExerciseSwapContext>
  ): boolean {
    return swaps[exerciseId] !== undefined
  }

  /**
   * Get swap context for a specific exercise
   */
  static getSwapForExercise(
    workoutId: number,
    exerciseId: number,
    swaps: Record<number, ExerciseSwapContext>
  ): ExerciseSwapContext | null {
    const swap = swaps[exerciseId]
    return swap && swap.workoutId === workoutId ? swap : null
  }

  /**
   * Clean up old swaps (optional - for maintenance)
   */
  static cleanupOldSwaps(
    swaps: Record<number, ExerciseSwapContext>,
    maxAge: number = 30 * 24 * 60 * 60 * 1000 // 30 days
  ): Record<number, ExerciseSwapContext> {
    const now = Date.now()

    return Object.fromEntries(
      Object.entries(swaps).filter(([, swap]) => {
        const swapAge = now - new Date(swap.swapDate).getTime()
        return swapAge < maxAge
      })
    )
  }
}

// Export singleton instance
export const swapExerciseService = SwapExerciseService.getInstance()
