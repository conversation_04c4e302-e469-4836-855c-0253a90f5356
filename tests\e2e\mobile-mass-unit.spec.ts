import { test, expect } from '@playwright/test'
import { login } from './helpers'
import { setupAuth } from './helpers/auth-helper'

test.describe('Mobile Mass Unit Preference', () => {
  test.beforeEach(async ({ page, context }) => {
    // Set up mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    // Set up authentication with user that has lbs preference
    await setupAuth(context, '<EMAIL>')

    // Mock user info with mass unit preference
    await page.route('**/api/Account/GetUserInfo*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Email: '<EMAIL>',
          FirstName: 'Test',
          LastName: 'User',
          MassUnit: 'lbs', // Ensure user prefers lbs
        }),
      })
    })
  })

  test('should respect user mass unit preference in SetScreen', async ({
    page,
  }) => {
    // Navigate to login and login
    await page.goto('/login')
    await login(page, '<EMAIL>', 'Dr123456')
    await page.waitForURL('**/workout')

    // Navigate to first exercise
    await page.getByTestId('exercise-0').click()
    await page.waitForURL('**/workout/exercise/**')

    // Check that weight displays use the correct unit
    // The mock user data has MassUnit: 'lbs'
    await expect(page.getByText(/lbs/)).toBeVisible()

    // Check the weight input shows lbs
    const weightInput = page.getByLabel('Weight')
    await expect(weightInput).toBeVisible()

    // Check that previous performance shows lbs
    const previousPerf = page.getByText(/Last:.*lbs/)
    if (await previousPerf.isVisible()) {
      await expect(previousPerf).toContainText('lbs')
    }
  })

  test('should handle null Weight safely', async ({ page }) => {
    // Navigate to login and login
    await page.goto('/login')
    await login(page, '<EMAIL>', 'Dr123456')
    await page.waitForURL('**/workout')

    // Navigate to exercise
    await page.getByTestId('exercise-0').click()
    await page.waitForURL('**/workout/exercise/**')

    // Page should load without errors even if some weights are null
    await expect(page).not.toHaveTitle(/error/i)

    // Check that the UI renders properly
    await expect(page.getByRole('button', { name: /save set/i })).toBeVisible()
  })
})
