import { useMemo } from 'react'
import type {
  WorkoutLogSerieModelRef,
  ExerciseWorkSetsModel,
  MassUnit,
} from '@/types/api/WorkoutLogSerieModelRef'
import type { RecommendationModel, ExerciseModel } from '@/types/api'
import { createWarmupSet, createWorkSet } from './useSetListMobileHelpers'

export function useSetListMobile(
  exercise: ExerciseModel | null,
  recommendation: RecommendationModel | null,
  massUnit: MassUnit = 'kg'
) {
  const exerciseWorkSets = useMemo<ExerciseWorkSetsModel | null>(() => {
    if (!exercise || !recommendation) return null

    const sets: WorkoutLogSerieModelRef[] = []

    // Add warmup sets
    if (recommendation.WarmUpsList) {
      const totalWarmups = recommendation.WarmUpsList.length
      recommendation.WarmUpsList.forEach((warmup, i) => {
        sets.push(
          createWarmupSet(
            warmup,
            i,
            exercise,
            recommendation,
            massUnit,
            totalWarmups
          )
        )
      })
    }

    // Add work sets
    const numWorkSets = recommendation.Series ?? 0 // Don't generate sets if Series is undefined or null
    for (let i = 0; i < numWorkSets; i++) {
      sets.push(
        createWorkSet(i, exercise, recommendation, massUnit, numWorkSets)
      )
    }

    return {
      Id: exercise.Id,
      Label: exercise.Label,
      CountNo: '1',
      IsBodyweight: exercise.IsBodyweight || false,
      IsSystemExercise: exercise.IsSystemExercise || false,
      IsFlexibility: exercise.IsFlexibility || false,
      IsTimeBased: exercise.IsTimeBased || false,
      IsUnilateral: exercise.IsUnilateral || false,
      IsFinished: false,
      IsNextExercise: true,
      IsSelected: false,
      IsRecoLoaded: true,
      VideoUrl: exercise.VideoUrl || '',
      HeaderImage: '',
      BodyPartId: exercise.BodyPartId,
      Sets: sets,
      Clear() {
        this.Sets = []
      },
      get Count() {
        return this.Sets.length
      },
      Add(set: WorkoutLogSerieModelRef) {
        this.Sets.push(set)
      },
    }
  }, [exercise, recommendation, massUnit])

  const updateSetState = (
    setIndex: number,
    updates: Partial<WorkoutLogSerieModelRef>
  ) => {
    if (!exerciseWorkSets) return

    const set = exerciseWorkSets.Sets[setIndex]
    if (!set) return

    Object.assign(set, updates)
  }

  const markSetComplete = (setIndex: number) => {
    if (!exerciseWorkSets) return

    updateSetState(setIndex, { IsFinished: true, IsNext: false })

    // Move IsNext to next unfinished set
    const nextIndex = exerciseWorkSets.Sets.findIndex(
      (s, i) => i > setIndex && !s.IsFinished && !s.IsWarmups
    )
    if (nextIndex !== -1) {
      updateSetState(nextIndex, { IsNext: true })
    }
  }

  return {
    exerciseWorkSets,
    massUnit,
    updateSetState,
    markSetComplete,
  }
}
