/* eslint-disable react-hooks/rules-of-hooks */
import { Page, test as base } from '@playwright/test'

/**
 * WebKit-specific test helpers for enhanced stability
 */
export const webkitHelpers = {
  /**
   * Wait for page to be fully stable before interactions
   */
  async waitForStability(page: Page): Promise<void> {
    // Wait for network idle
    await page.waitForLoadState('networkidle', { timeout: 30000 })

    // Additional stability wait for WebKit
    await page.waitForTimeout(500)

    // Ensure no pending animations
    await page.evaluate(() => {
      return new Promise<void>((resolve) => {
        requestAnimationFrame(() => {
          requestAnimationFrame(() => {
            resolve()
          })
        })
      })
    })
  },

  /**
   * Safe navigation with WebKit-specific handling
   */
  async safeGoto(page: Page, url: string): Promise<void> {
    const maxRetries = 3
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // eslint-disable-next-line no-await-in-loop
        await page.goto(url, {
          waitUntil: 'domcontentloaded',
          timeout: 45000,
        })

        // Wait for stability
        // eslint-disable-next-line no-await-in-loop
        await this.waitForStability(page)

        return
      } catch (error) {
        lastError = error as Error
        console.warn(`Navigation attempt ${attempt} failed:`, error)

        if (attempt < maxRetries) {
          // eslint-disable-next-line no-await-in-loop
          await page.waitForTimeout(1000 * attempt)
        }
      }
    }

    throw new Error(
      `Failed to navigate after ${maxRetries} attempts: ${lastError}`
    )
  },

  /**
   * Safe click with WebKit-specific handling
   */
  async safeClick(page: Page, selector: string): Promise<void> {
    // Wait for element to be stable
    await page.waitForSelector(selector, { state: 'visible', timeout: 30000 })

    // Ensure element is in viewport
    await page.locator(selector).scrollIntoViewIfNeeded()

    // Wait a bit for WebKit stability
    await page.waitForTimeout(200)

    // Click with retry
    const maxRetries = 3
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // eslint-disable-next-line no-await-in-loop
        await page.click(selector, { timeout: 10000 })
        return
      } catch (error) {
        if (attempt === maxRetries) throw error
        // eslint-disable-next-line no-await-in-loop
        await page.waitForTimeout(500 * attempt)
      }
    }
  },

  /**
   * Safe fill with WebKit-specific handling
   */
  async safeFill(page: Page, selector: string, value: string): Promise<void> {
    // Wait for element
    await page.waitForSelector(selector, { state: 'visible', timeout: 30000 })

    // Clear and fill with stability delays
    await page.click(selector)
    await page.waitForTimeout(100)

    // Clear existing value
    await page.fill(selector, '')
    await page.waitForTimeout(100)

    // Fill new value
    await page.fill(selector, value)
    await page.waitForTimeout(100)
  },

  /**
   * Check if browser context is still alive
   */
  async isContextAlive(page: Page): Promise<boolean> {
    try {
      await page.evaluate(() => true)
      return true
    } catch {
      return false
    }
  },
}

/**
 * Extended test with WebKit stability helpers
 */
export const test = base.extend<{
  webkitStableTest: (testFn: (page: Page) => Promise<void>) => Promise<void>
}>({
  webkitStableTest: async ({ page }, use) => {
    await use(async (testFn) => {
      // Add error handler for page crashes
      page.on('crash', () => {
        throw new Error('Page crashed during test')
      })

      // Run test with stability wrapper
      try {
        await testFn(page)
      } catch (error) {
        // Check if it's a browser context error
        const errorMessage = error.toString()
        if (
          errorMessage.includes(
            'Target page, context or browser has been closed'
          )
        ) {
          throw new Error(
            'Browser context was closed unexpectedly. This is a known WebKit stability issue.'
          )
        }
        throw error
      }
    })
  },
})
