'use client'

import React from 'react'
import { useSetInputHandlers } from '../../hooks/useSetInputHandlers'
import { RepsInput } from './inputs/RepsInput'
import { DurationInput } from './inputs/DurationInput'
import { WeightInput } from './inputs/WeightInput'

interface SetInputsProps {
  reps?: number
  weight: number
  unit: 'lbs' | 'kg'
  increments?: { Kg: number; Lb: number }
  onChange: (data: {
    reps?: number
    weight?: number
    duration?: number
  }) => void
  disabled?: boolean
  isBodyweight?: boolean
  isTimeBased?: boolean
  duration?: number
}

export function SetInputs({
  reps,
  weight,
  unit,
  increments,
  onChange,
  disabled = false,
  isBodyweight = false,
  isTimeBased = false,
  duration = 0,
}: SetInputsProps) {
  const {
    errors,
    handleRepsChange,
    handleWeightChange,
    handleDurationChange,
    incrementWeight,
    decrementWeight,
    incrementReps,
    decrementReps,
  } = useSetInputHandlers({
    reps,
    weight,
    duration,
    unit,
    isBodyweight,
    isTimeBased,
    increments,
    onChange,
  })

  return (
    <div className="space-y-6">
      {!isTimeBased ? (
        <RepsInput
          reps={reps}
          onChange={handleRepsChange}
          onIncrement={incrementReps}
          onDecrement={decrementReps}
          disabled={disabled}
          error={errors.reps}
        />
      ) : (
        <DurationInput
          duration={duration}
          onChange={handleDurationChange}
          disabled={disabled}
          error={errors.duration}
        />
      )}

      <WeightInput
        weight={weight}
        unit={unit}
        onChange={handleWeightChange}
        onIncrement={incrementWeight}
        onDecrement={decrementWeight}
        disabled={disabled}
        error={errors.weight}
        isBodyweight={isBodyweight}
      />
    </div>
  )
}
