Based on my analysis of the mobile app code, here's a comprehensive breakdown of how the Dr. Muscle mobile app saves workout data to the database:

API Endpoints Used
Save Individual Set: /api/Exercise/AddWorkoutLogSerieNew
Save Multiple Sets: /api/Exercise/AddWorkoutLogSerieListNew
Complete Workout: /api/Workout/SaveWorkoutV3Pro
Data Flow Overview

1. During Exercise (SaveSetPage.xaml.cs)
   User performs sets and enters weight/reps
   Each set is saved individually to the server using AddWorkoutLogSerieNew
   RIR (Reps in Reserve) is collected after the first work set
   Warm-up sets are marked with IsWarmups = true
2. Exercise Completion
   When user taps "Finish Exercise", all sets for that exercise have already been saved
   The app tracks which exercises are completed
3. Workout Completion
   When the entire workout is finished, SaveWorkoutV3Pro is called
   This finalizes the workout session
   Detailed Implementation Plan
   Step 1: Data Models
   typescript

// WorkoutLogSerieModel equivalent
interface WorkoutLogSerie {
id?: number;
exercice: {
id: number;
};
userId: string;
reps: number;
weight: {
kg: number;
lb: number;
};
RIR?: number; // Reps in Reserve (0-4)
isWarmups: boolean;
logDate?: Date;
}

// SaveWorkoutModel equivalent
interface SaveWorkoutModel {
workoutId: number;
workoutTemplateId?: number;
}
Step 2: Save Individual Set
typescript

// API service method
async function saveWorkoutSet(setData: WorkoutLogSerie): Promise<boolean> {
try {
const response = await fetch('/api/Exercise/AddWorkoutLogSerieNew', {
method: 'POST',
headers: {
'Content-Type': 'application/json',
'Authorization': `Bearer ${getAuthToken()}`
},
body: JSON.stringify(setData)
});

    const result = await response.json();
    return result.result;

} catch (error) {
console.error('Failed to save set:', error);
return false;
}
}
Step 3: Tracking Warm-ups and Work Sets
typescript

class ExerciseTracker {
private sets: WorkoutLogSerie[] = [];
private warmupCount: number = 0;
private isFirstWorkSet: boolean = true;

addSet(weight: number, reps: number, exerciseId: number) {
const isWarmup = this.sets.length < this.warmupCount;

    const set: WorkoutLogSerie = {
      exercice: { id: exerciseId },
      userId: getCurrentUserId(),
      reps: reps,
      weight: {
        kg: weight, // Convert based on user preference
        lb: weight * 2.20462
      },
      isWarmups: isWarmup,
      RIR: undefined
    };

    // First work set needs RIR
    if (!isWarmup && this.isFirstWorkSet) {
      this.isFirstWorkSet = false;
      // Trigger RIR collection
      return { set, needsRIR: true };
    }

    this.sets.push(set);
    return { set, needsRIR: false };

}
}
Step 4: RIR Collection After First Work Set
typescript

// Component for RIR selection
function collectRIR(): Promise<number> {
return new Promise((resolve) => {
// Show modal/dialog with options:
const rirOptions = [
{ value: 0, label: "That was very, very hard" },
{ value: 1, label: "I could have done 1-2 more reps" },
{ value: 2, label: "I could have done 3-4 more reps" },
{ value: 3, label: "I could have done 5-6 more reps" },
{ value: 4, label: "I could have done 7+ more reps" }
];

    // User selects option, then resolve with value

});
}

// Usage in exercise flow
async function saveSetWithRIR(set: WorkoutLogSerie, needsRIR: boolean) {
if (needsRIR) {
const rir = await collectRIR();
set.RIR = rir;
}

const success = await saveWorkoutSet(set);
return success;
}
Step 5: Complete Exercise Flow
typescript

class ExerciseSession {
private exerciseId: number;
private sets: WorkoutLogSerie[] = [];
private recommendedSets: number = 3;
private warmupSets: number = 2;

async finishExercise() {
// All sets have already been saved individually
// Just update local state
markExerciseAsCompleted(this.exerciseId);

    // Move to next exercise or finish workout
    if (hasMoreExercises()) {
      navigateToNextExercise();
    } else {
      promptToFinishWorkout();
    }

}
}
Step 6: Complete Workout
typescript

async function finishAndSaveWorkout(workoutId: number, templateId?: number) {
try {
const saveModel: SaveWorkoutModel = {
workoutId: workoutId,
workoutTemplateId: templateId
};

    const response = await fetch('/api/Workout/SaveWorkoutV3Pro', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getAuthToken()}`
      },
      body: JSON.stringify(saveModel)
    });

    const result = await response.json();

    if (result.result) {
      // Success - navigate to workout summary
      showWorkoutComplete();
    }

    return result.result;

} catch (error) {
console.error('Failed to save workout:', error);
return false;
}
}
Step 7: Complete Implementation Example
typescript

// Exercise component
export function ExerciseScreen({ exercise, workout }) {
const [sets, setSets] = useState<WorkoutLogSerie[]>([]);
const [currentWeight, setCurrentWeight] = useState(0);
const [currentReps, setCurrentReps] = useState(0);
const [waitingForRIR, setWaitingForRIR] = useState(false);

const warmupCount = exercise.warmUpsList.length;
const isWarmup = sets.length < warmupCount;
const isFirstWorkSet = sets.length === warmupCount;

async function handleSaveSet() {
const newSet: WorkoutLogSerie = {
exercice: { id: exercise.id },
userId: getUserId(),
reps: currentReps,
weight: {
kg: currentWeight,
lb: currentWeight \* 2.20462
},
isWarmups: isWarmup,
RIR: undefined
};

    // Check if this is the first work set
    if (isFirstWorkSet) {
      setWaitingForRIR(true);
      const rir = await showRIRDialog();
      newSet.RIR = rir;
      setWaitingForRIR(false);
    }

    // Save to backend
    const success = await saveWorkoutSet(newSet);

    if (success) {
      setSets([...sets, newSet]);
      // Update UI for next set
      updateRecommendations();
    }

}

async function handleFinishExercise() {
// Confirm with user
const confirmed = await confirm("Finish exercise?",
"Are you sure you are finished and want to save this exercise?");

    if (confirmed) {
      // Mark exercise as complete locally
      markExerciseComplete(exercise.id);

      // Navigate to next exercise or workout completion
      if (workout.hasMoreExercises()) {
        navigateToExercise(workout.nextExercise());
      } else {
        // All exercises done, save workout
        await finishAndSaveWorkout(workout.id, workout.templateId);
        navigateToWorkoutSummary();
      }
    }

}

return (

<div>
{/_ Exercise UI _/}
<button onClick={handleSaveSet}>Save Set</button>
<button onClick={handleFinishExercise}>Finish Exercise</button>
</div>
);
}
Key Points to Remember
Individual Set Saving: Each set is saved immediately after completion, not batched
RIR Collection: Only collected after the first work set (not warmups)
Warmup Detection: Sets are marked as warmups based on position in the set list
Exercise Completion: No special API call needed - sets are already saved
Workout Completion: Final API call to mark the entire workout as complete
Error Handling: Always handle API failures gracefully and allow retry
