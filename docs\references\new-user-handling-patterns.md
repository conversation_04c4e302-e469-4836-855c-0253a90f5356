# New User Handling Patterns in Dr. Muscle X

## Overview

This document describes how the Dr. Muscle X codebase handles new users who don't have profile data yet. The app uses a graceful degradation approach where 404 errors are expected and handled appropriately.

## Key Patterns

### 1. 404 Error Handling for New Users

The app expects 404 errors when fetching user info for new users:

```typescript
// src/hooks/useAuth.ts (lines 54-66)
const userInfo = await userProfileApi.getUserInfo().catch((error) => {
  // 404 is expected for new users - don't log as warning
  if (error?.response?.status === 404) {
    debugLog('[Login] User info not found - likely a new user')
  } else {
    debugLog.warn('[Login] Failed to prefetch user info:', error.message)
  }
  endSession(sessionId, false)
  return null
})
```

**Key Points:**

- 404 errors for user info are NOT logged as warnings
- The app continues normally without user profile data
- No client-side profile creation is attempted

### 2. OAuth New User Detection

OAuth responses include an `isNewUser` flag:

```typescript
// src/types/oauth.ts (lines 86-88)
export interface OAuthLoginResponse extends LoginSuccessResult {
  /** Whether this is a new user registration */
  isNewUser?: boolean
  // ... other fields
}
```

This allows the backend to indicate when a user is newly created during OAuth login.

### 3. Welcome Messages for New Users

The app provides special welcome messages for users at different stages:

```typescript
// src/utils/welcomeMessages.ts
// For users with 0 workouts (line 156)
const workoutCountText =
  totalWorkouts > 0
    ? `${totalWorkouts} workout${totalWorkouts === 1 ? '' : 's'}`
    : 'First workout'

// For users with 1 workout (line 112)
if (totalWorkouts === 1) {
  return '🎉 Your fitness journey begins today!'
}

// For users with no streak (line 150)
const streakText =
  streak > 0 ? `${streak} day${streak === 1 ? '' : ''} streak` : 'New journey'
```

### 4. Program Data Handling

Similar to user info, the app gracefully handles missing program data:

```typescript
// src/hooks/useAuth.ts (lines 107-116)
const program = await programApi.getUserProgram().catch((error) => {
  // Gracefully handle new users or users without programs
  if (error?.message?.includes('No program info')) {
    debugLog('[Login] No program data - user may be new')
  } else {
    debugLog.warn('Failed to prefetch program:', error.message)
  }
  return null
})
```

### 5. Test Coverage

The codebase includes specific tests for new user scenarios:

```typescript
// src/hooks/__tests__/useAuth.prefetch-fix.test.ts (line 24)
it('should handle user info prefetch failure gracefully for new users', async () => {
  // User info returns 404 (new user)
  vi.mocked(userProfileApi.getUserInfo).mockRejectedValue({
    response: { status: 404 },
    message: 'Not found',
  })

  // ... test continues to verify graceful handling
})
```

## Backend Responsibilities

Based on the patterns observed, the backend is responsible for:

1. **User Creation**: Creating user records when they first register/login
2. **Initial Setup**: Setting up default values for new users
3. **Program Assignment**: Assigning initial workout programs
4. **Profile Defaults**: Creating default profile values

## Client-Side Behavior

The client (PWA) handles new users by:

1. **Graceful Degradation**: Continuing to function without profile/program data
2. **Visual Adaptation**: Showing appropriate messages for new users
3. **Background Prefetch**: Attempting to fetch data but not blocking on failures
4. **No Local Creation**: Not attempting to create profiles client-side

## Summary

The Dr. Muscle X PWA handles new users through a "fail gracefully" approach:

- 404 errors for user data are expected and handled silently
- The UI adapts to show appropriate messages for new users
- All user creation and setup is handled server-side
- The client never blocks the user experience due to missing profile data

This approach ensures a smooth onboarding experience where new users can immediately start using the app while their profile data is being set up in the background by the backend services.
