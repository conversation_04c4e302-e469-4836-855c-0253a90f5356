import { renderHook, act } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { useExercisePageInitialization } from '../useExercisePageInitialization'
import { useRouter } from 'next/navigation'
import { useWorkout } from '../useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'

// Mock dependencies
vi.mock('next/navigation')
vi.mock('../useWorkout')
vi.mock('@/stores/workoutStore')
vi.mock('@/utils/debugLog', () => ({
  debugLog: Object.assign(vi.fn(), {
    log: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
  }),
}))

describe('useExercisePageInitialization', () => {
  const mockRouter = {
    push: vi.fn(),
    replace: vi.fn(),
  }

  const mockWorkout = {
    todaysWorkout: [
      {
        WorkoutTemplates: [{ Id: 1, Name: 'Test Workout' }],
      },
    ],
    isLoadingWorkout: false,
    startWorkout: vi.fn().mockResolvedValue({ success: true }),
    exercises: [
      { Id: 123, Label: 'Bench Press', sets: [] },
      { Id: 456, Label: 'Squat', sets: [] },
    ],
    workoutSession: null,
    loadRecommendation: vi.fn(),
    updateExerciseWorkSets: vi.fn(),
  }

  const mockStore = {
    setCurrentExerciseById: vi.fn(),
    loadingStates: new Map(),
    getCachedExerciseRecommendation: vi.fn().mockReturnValue(null),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useRouter).mockReturnValue(mockRouter as any)
    vi.mocked(useWorkout).mockReturnValue(mockWorkout as any)
    vi.mocked(useWorkoutStore).mockReturnValue(mockStore as any)
  })

  it('should not re-initialize when workout session already exists', async () => {
    // Setup: Workout session already exists
    const workoutWithSession = {
      ...mockWorkout,
      workoutSession: {
        id: 'session-123',
        startTime: new Date(),
        exercises: [],
        exerciseRIRStatus: {},
      },
    }
    vi.mocked(useWorkout).mockReturnValue(workoutWithSession as any)

    // Render the hook
    const { result } = renderHook(() => useExercisePageInitialization(123))

    // Wait for initialization
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 100))
    })

    // Verify: Should not try to start workout again
    expect(mockWorkout.startWorkout).not.toHaveBeenCalled()
    expect(result.current.isInitializing).toBe(false)
    expect(result.current.loadingError).toBeNull()

    // Verify: Should still set current exercise
    expect(mockStore.setCurrentExerciseById).toHaveBeenCalledWith(123)
  })

  it('should start workout if no session exists', async () => {
    // Render the hook
    const { result } = renderHook(() => useExercisePageInitialization(123))

    // Wait for initialization
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 100))
    })

    // Verify: Should start workout
    expect(mockWorkout.startWorkout).toHaveBeenCalledWith(
      mockWorkout.todaysWorkout
    )
    expect(result.current.isInitializing).toBe(false)
    expect(result.current.loadingError).toBeNull()
  })

  it('should handle navigation back from rest timer without re-initialization', async () => {
    // Initial render with no session
    const { rerender } = renderHook(
      ({ exerciseId }) => useExercisePageInitialization(exerciseId),
      { initialProps: { exerciseId: 123 } }
    )

    // Wait for initial setup
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 100))
    })

    expect(mockWorkout.startWorkout).toHaveBeenCalledTimes(1)

    // Simulate workout session now exists (after saving sets)
    const workoutWithSession = {
      ...mockWorkout,
      workoutSession: {
        id: 'session-123',
        startTime: new Date(),
        exercises: [
          {
            exerciseId: 123,
            name: 'Bench Press',
            sets: [{ setNumber: 1, reps: 10, weight: { Kg: 60, Lb: 132 } }],
          },
        ],
        exerciseRIRStatus: {},
      },
    }
    vi.mocked(useWorkout).mockReturnValue(workoutWithSession as any)

    // Simulate navigation back from rest timer (component re-mounts)
    rerender({ exerciseId: 123 })

    // Wait for re-initialization attempt
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 100))
    })

    // Verify: Should NOT start workout again
    expect(mockWorkout.startWorkout).toHaveBeenCalledTimes(1) // Still only 1 call
    expect(mockStore.setCurrentExerciseById).toHaveBeenLastCalledWith(123)
  })

  it('should load recommendation only if not cached', async () => {
    // Mock that recommendation is already cached
    mockStore.getCachedExerciseRecommendation.mockReturnValue({
      Reps: 10,
      Weight: { Kg: 60, Lb: 132 },
    })

    const { result } = renderHook(() => useExercisePageInitialization(123))

    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 100))
    })

    // Should not load recommendation again
    expect(mockWorkout.loadRecommendation).not.toHaveBeenCalled()
    expect(result.current.isInitializing).toBe(false)
  })

  it('should handle errors during initialization', async () => {
    // Mock no workout templates
    const emptyWorkout = {
      ...mockWorkout,
      todaysWorkout: [
        {
          WorkoutTemplates: [],
        },
      ],
    }
    vi.mocked(useWorkout).mockReturnValue(emptyWorkout as any)

    const { result } = renderHook(() => useExercisePageInitialization(123))

    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 100))
    })

    // Should redirect to workout page when no templates
    expect(mockRouter.replace).toHaveBeenCalledWith('/workout')
    expect(result.current.isInitializing).toBe(false)
  })

  it('should retry initialization on manual retry', async () => {
    // Setup error state
    mockWorkout.startWorkout.mockRejectedValueOnce(new Error('Network error'))

    const { result } = renderHook(() => useExercisePageInitialization(123))

    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 100))
    })

    expect(result.current.loadingError).toBeTruthy()

    // Reset mock to succeed
    mockWorkout.startWorkout.mockResolvedValue({ success: true })

    // Retry
    await act(async () => {
      await result.current.retryInitialization()
    })

    expect(result.current.loadingError).toBeNull()
    expect(mockWorkout.startWorkout).toHaveBeenCalledTimes(2)
  })
})
