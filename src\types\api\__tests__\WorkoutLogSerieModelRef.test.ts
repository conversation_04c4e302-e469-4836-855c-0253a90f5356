/**
 * TDD Tests for WorkoutLogSerieModelRef
 *
 * Test suite for mobile exercise page set state management
 */

import { describe, it, expect, beforeEach } from 'vitest'
import type {
  WorkoutLogSerieModelRef,
  ExerciseWorkSetsModel,
  ExerciseDisplaySettings,
} from '../WorkoutLogSerieModelRef'
import type { MultiUnityWeight } from '../workout'

describe('WorkoutLogSerieModelRef', () => {
  let mockSet: WorkoutLogSerieModelRef
  let mockWeight: MultiUnityWeight

  beforeEach(() => {
    mockWeight = { Kg: 60, Lb: 132.28 }

    mockSet = {
      // Base WorkoutLogSerieModel properties
      Id: 1,
      ExerciseId: 217,
      SerieOrder: 1,
      Reps: 8,
      Weight: mockWeight,
      IsWarmups: false,
      RIR: 2,
      IsAssisted: false,

      // WorkoutLogSerieModelRef specific properties
      ExerciseName: 'Barbell bench press',
      EquipmentId: 1,
      IsNext: false,
      IsFinished: false,
      IsActive: false,
      IsEditing: false,
      SetNo: '1',
      SetTitle: 'Set 1',
      IsLastSet: false,
      IsFirstSide: true,
      IsHeaderCell: false,
      IsFirstSetFinished: false,
      IsFirstWorkSet: true,
      IsExerciseFinished: false,
      IsJustSetup: false,
      ShouldUpdateIncrement: false,
      IsBackOffSet: false,
      IsNextBackOffSet: false,
      IsDropSet: false,
      IsNormalset: true,
      IsMaxChallenge: false,
      IsFlexibility: false,
      IsTimeBased: false,
      IsUnilateral: false,
      IsBodyweight: false,
      IsTimerOff: false,
      IsSizeChanged: false,
      ShowWorkTimer: true,
      LastTimeSet: '2024-01-10',
      PreviousReps: 8,
      PreviousWeight: { Kg: 55, Lb: 121.25 },
      Speed: 1.0,
      Increments: { Kg: 2.5, Lb: 5 },
      Min: { Kg: 20, Lb: 45 },
      Max: { Kg: 200, Lb: 440 },
      HeaderImage: '',
      HeaderTitle: '',
      VideoUrl: '',
      ShowPlusTooltip: false,
      ShowSuperSet3: false,
      ShowSuperSet2: false,

      // Computed properties
      get BackColor() {
        if (this.IsFinished || this.IsNext) {
          return '#4D0C2432' // Android color
        }
        return 'transparent'
      },

      get WeightSingal() {
        // Simulate kg unit preference
        const isKg = true
        return isKg
          ? Number(this.Weight.Kg).toFixed(2)
          : Number(this.Weight.Lb).toFixed(2)
      },

      get WeightDouble() {
        const isKg = true
        return isKg
          ? Number(this.Weight.Kg).toFixed(2)
          : Number(this.Weight.Lb).toFixed(2)
      },
    }
  })

  describe('Set State Management', () => {
    it('should have correct initial state', () => {
      expect(mockSet.IsNext).toBe(false)
      expect(mockSet.IsFinished).toBe(false)
      expect(mockSet.IsActive).toBe(false)
      expect(mockSet.IsEditing).toBe(false)
    })

    it('should handle IsNext state change', () => {
      mockSet.IsNext = true
      expect(mockSet.IsNext).toBe(true)
      expect(mockSet.BackColor).toBe('#4D0C2432')
    })

    it('should handle IsFinished state change', () => {
      mockSet.IsFinished = true
      expect(mockSet.IsFinished).toBe(true)
      expect(mockSet.BackColor).toBe('#4D0C2432')
    })

    it('should return transparent background when not active', () => {
      mockSet.IsNext = false
      mockSet.IsFinished = false
      expect(mockSet.BackColor).toBe('transparent')
    })
  })

  describe('Weight Display', () => {
    it('should format weight correctly for kg', () => {
      expect(mockSet.WeightSingal).toBe('60.00')
      expect(mockSet.WeightDouble).toBe('60.00')
    })

    it('should handle decimal weights', () => {
      mockSet.Weight = { Kg: 62.5, Lb: 137.79 }
      expect(mockSet.WeightSingal).toBe('62.50')
    })

    it('should round to 2 decimal places', () => {
      mockSet.Weight = { Kg: 60.123, Lb: 132.456 }
      expect(mockSet.WeightSingal).toBe('60.12')
    })
  })

  describe('Set Type Identification', () => {
    it('should identify normal sets', () => {
      expect(mockSet.IsNormalset).toBe(true)
      expect(mockSet.IsBackOffSet).toBe(false)
      expect(mockSet.IsDropSet).toBe(false)
      expect(mockSet.IsMaxChallenge).toBe(false)
    })

    it('should handle back-off sets', () => {
      mockSet.IsBackOffSet = true
      mockSet.IsNormalset = false
      expect(mockSet.IsBackOffSet).toBe(true)
      expect(mockSet.IsNormalset).toBe(false)
    })

    it('should handle drop sets', () => {
      mockSet.IsDropSet = true
      mockSet.IsNormalset = false
      expect(mockSet.IsDropSet).toBe(true)
      expect(mockSet.IsNormalset).toBe(false)
    })

    it('should handle max challenge sets', () => {
      mockSet.IsMaxChallenge = true
      mockSet.IsNormalset = false
      expect(mockSet.IsMaxChallenge).toBe(true)
      expect(mockSet.IsNormalset).toBe(false)
    })
  })

  describe('Exercise Characteristics', () => {
    it('should handle bodyweight exercises', () => {
      mockSet.IsBodyweight = true
      expect(mockSet.IsBodyweight).toBe(true)
    })

    it('should handle time-based exercises', () => {
      mockSet.IsTimeBased = true
      expect(mockSet.IsTimeBased).toBe(true)
    })

    it('should handle unilateral exercises', () => {
      mockSet.IsUnilateral = true
      expect(mockSet.IsUnilateral).toBe(true)
    })

    it('should handle flexibility exercises', () => {
      mockSet.IsFlexibility = true
      expect(mockSet.IsFlexibility).toBe(true)
    })
  })

  describe('Set Position and Metadata', () => {
    it('should have correct set number', () => {
      expect(mockSet.SetNo).toBe('1')
      expect(mockSet.SetTitle).toBe('Set 1')
    })

    it('should handle last set flag', () => {
      mockSet.IsLastSet = true
      expect(mockSet.IsLastSet).toBe(true)
    })

    it('should handle first work set flag', () => {
      expect(mockSet.IsFirstWorkSet).toBe(true)
    })

    it('should handle header cell flag', () => {
      mockSet.IsHeaderCell = true
      expect(mockSet.IsHeaderCell).toBe(true)
    })
  })

  describe('Weight and Progression', () => {
    it('should store previous performance data', () => {
      expect(mockSet.PreviousReps).toBe(8)
      expect(mockSet.PreviousWeight.Kg).toBe(55)
      expect(mockSet.PreviousWeight.Lb).toBe(121.25)
    })

    it('should store increment data', () => {
      expect(mockSet.Increments.Kg).toBe(2.5)
      expect(mockSet.Increments.Lb).toBe(5)
    })

    it('should store min/max weight limits', () => {
      expect(mockSet.Min.Kg).toBe(20)
      expect(mockSet.Max.Kg).toBe(200)
    })

    it('should handle speed setting', () => {
      mockSet.Speed = 0.8
      expect(mockSet.Speed).toBe(0.8)
    })
  })
})

describe('ExerciseWorkSetsModel', () => {
  let mockExercise: ExerciseWorkSetsModel

  beforeEach(() => {
    mockExercise = {
      Id: 217,
      Label: 'Barbell bench press',
      CountNo: '1',
      IsBodyweight: false,
      IsSystemExercise: true,
      IsFlexibility: false,
      IsTimeBased: false,
      IsUnilateral: false,
      IsFinished: false,
      IsNextExercise: true,
      IsSelected: false,
      IsRecoLoaded: false,
      VideoUrl: 'https://example.com/video.mp4',
      HeaderImage: '',
      BodyPartId: 1,
      Sets: [],
      Clear() {
        this.Sets = []
      },
      get Count() {
        return this.Sets.length
      },
      Add(set: WorkoutLogSerieModelRef) {
        this.Sets.push(set)
      },
    }
  })

  describe('Exercise Properties', () => {
    it('should have correct exercise identification', () => {
      expect(mockExercise.Id).toBe(217)
      expect(mockExercise.Label).toBe('Barbell bench press')
      expect(mockExercise.CountNo).toBe('1')
    })

    it('should handle exercise characteristics', () => {
      expect(mockExercise.IsBodyweight).toBe(false)
      expect(mockExercise.IsSystemExercise).toBe(true)
      expect(mockExercise.IsFlexibility).toBe(false)
      expect(mockExercise.IsTimeBased).toBe(false)
      expect(mockExercise.IsUnilateral).toBe(false)
    })

    it('should handle exercise state', () => {
      expect(mockExercise.IsFinished).toBe(false)
      expect(mockExercise.IsNextExercise).toBe(true)
      expect(mockExercise.IsSelected).toBe(false)
      expect(mockExercise.IsRecoLoaded).toBe(false)
    })
  })

  describe('Sets Collection Management', () => {
    it('should start with empty sets collection', () => {
      expect(mockExercise.Sets.length).toBe(0)
      expect(mockExercise.Count).toBe(0)
    })

    it('should add sets to collection', () => {
      const mockSet: WorkoutLogSerieModelRef = {
        Id: 1,
        ExerciseId: 217,
        SerieOrder: 1,
        Reps: 8,
        Weight: { Kg: 60, Lb: 132.28 },
        IsWarmups: false,
        RIR: 2,
        IsAssisted: false,
        ExerciseName: 'Barbell bench press',
        IsNext: false,
        IsFinished: false,
        IsActive: false,
        IsEditing: false,
        SetNo: '1',
        SetTitle: 'Set 1',
        IsLastSet: false,
        IsFirstSide: true,
        IsHeaderCell: false,
        IsFirstSetFinished: false,
        IsFirstWorkSet: true,
        IsExerciseFinished: false,
        IsJustSetup: false,
        ShouldUpdateIncrement: false,
        IsBackOffSet: false,
        IsNextBackOffSet: false,
        IsDropSet: false,
        IsNormalset: true,
        IsMaxChallenge: false,
        IsFlexibility: false,
        IsTimeBased: false,
        IsUnilateral: false,
        IsBodyweight: false,
        IsTimerOff: false,
        IsSizeChanged: false,
        ShowWorkTimer: true,
        LastTimeSet: '2024-01-10',
        PreviousReps: 8,
        PreviousWeight: { Kg: 55, Lb: 121.25 },
        Speed: 1.0,
        Increments: { Kg: 2.5, Lb: 5 },
        Min: { Kg: 20, Lb: 45 },
        Max: { Kg: 200, Lb: 440 },
        HeaderImage: '',
        HeaderTitle: '',
        VideoUrl: '',
        ShowPlusTooltip: false,
        ShowSuperSet3: false,
        ShowSuperSet2: false,
        get BackColor() {
          return 'transparent'
        },
        get WeightSingal() {
          return '60.00'
        },
        get WeightDouble() {
          return '60.00'
        },
      }

      mockExercise.Add(mockSet)
      expect(mockExercise.Count).toBe(1)
      expect(mockExercise.Sets[0]).toBe(mockSet)
    })

    it('should clear sets collection', () => {
      const mockSet: WorkoutLogSerieModelRef = {
        Id: 1,
        ExerciseId: 217,
        SerieOrder: 1,
        Reps: 8,
        Weight: { Kg: 60, Lb: 132.28 },
        IsWarmups: false,
        RIR: 2,
        IsAssisted: false,
        ExerciseName: 'Barbell bench press',
        IsNext: false,
        IsFinished: false,
        IsActive: false,
        IsEditing: false,
        SetNo: '1',
        SetTitle: 'Set 1',
        IsLastSet: false,
        IsFirstSide: true,
        IsHeaderCell: false,
        IsFirstSetFinished: false,
        IsFirstWorkSet: true,
        IsExerciseFinished: false,
        IsJustSetup: false,
        ShouldUpdateIncrement: false,
        IsBackOffSet: false,
        IsNextBackOffSet: false,
        IsDropSet: false,
        IsNormalset: true,
        IsMaxChallenge: false,
        IsFlexibility: false,
        IsTimeBased: false,
        IsUnilateral: false,
        IsBodyweight: false,
        IsTimerOff: false,
        IsSizeChanged: false,
        ShowWorkTimer: true,
        LastTimeSet: '2024-01-10',
        PreviousReps: 8,
        PreviousWeight: { Kg: 55, Lb: 121.25 },
        Speed: 1.0,
        Increments: { Kg: 2.5, Lb: 5 },
        Min: { Kg: 20, Lb: 45 },
        Max: { Kg: 200, Lb: 440 },
        HeaderImage: '',
        HeaderTitle: '',
        VideoUrl: '',
        ShowPlusTooltip: false,
        ShowSuperSet3: false,
        ShowSuperSet2: false,
        get BackColor() {
          return 'transparent'
        },
        get WeightSingal() {
          return '60.00'
        },
        get WeightDouble() {
          return '60.00'
        },
      }

      mockExercise.Add(mockSet)
      expect(mockExercise.Count).toBe(1)

      mockExercise.Clear()
      expect(mockExercise.Count).toBe(0)
      expect(mockExercise.Sets.length).toBe(0)
    })
  })
})

describe('ExerciseDisplaySettings', () => {
  let settings: ExerciseDisplaySettings

  beforeEach(() => {
    settings = {
      massUnit: 'kg',
      showPlusTooltip: false,
      showSuperSet2: false,
      showSuperSet3: false,
      timerEnabled: true,
    }
  })

  it('should handle mass unit preferences', () => {
    expect(settings.massUnit).toBe('kg')

    settings.massUnit = 'lb'
    expect(settings.massUnit).toBe('lb')
  })

  it('should handle UI display flags', () => {
    expect(settings.showPlusTooltip).toBe(false)
    expect(settings.showSuperSet2).toBe(false)
    expect(settings.showSuperSet3).toBe(false)
    expect(settings.timerEnabled).toBe(true)
  })
})
