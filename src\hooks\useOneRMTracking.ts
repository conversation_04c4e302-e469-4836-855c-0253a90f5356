import { useMemo, useCallback } from 'react'
import { computeOneRM, calculateOneRMProgress } from '@/utils/oneRmCalculator'
import type { RecommendationModel, ExerciseModel } from '@/types/api'

interface UseOneRMTrackingProps {
  weight: number
  reps: number
  exercise: ExerciseModel | null
  recommendation: RecommendationModel | null
  unit: 'lbs' | 'kg'
  userBodyWeight: number
  /** @deprecated No longer used - 1RM is now shown for all sets */
  isFirstWorkSet?: boolean
  onWeightChange?: (value: number) => void
  onRepsChange?: (value: number) => void
}

export function useOneRMTracking({
  weight,
  reps,
  exercise,
  recommendation,
  unit,
  userBodyWeight,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  isFirstWorkSet: _isFirstWorkSet = true,
  onWeightChange,
  onRepsChange,
}: UseOneRMTrackingProps) {
  // Calculate effective weight based on exercise type
  const effectiveWeight = useMemo(() => {
    if (!exercise) return weight

    // For bodyweight exercises, add bodyweight to additional weight
    if (exercise.IsBodyweight || exercise.IsWeighted) {
      return weight + userBodyWeight
    }

    // For assisted exercises, subtract assistance from bodyweight
    if (exercise.IsAssisted) {
      return userBodyWeight - weight
    }

    // Regular weighted exercises
    return weight
  }, [weight, exercise, userBodyWeight])

  // Calculate current 1RM
  const currentOneRM = useMemo(() => {
    return computeOneRM(effectiveWeight, reps)
  }, [effectiveWeight, reps])

  // Calculate last 1RM from recommendation
  const lastOneRM = useMemo(() => {
    if (!recommendation?.FirstWorkSet1RM) return 0

    // Use the appropriate unit from recommendation
    return unit === 'kg'
      ? recommendation.FirstWorkSet1RM.Kg
      : recommendation.FirstWorkSet1RM.Lb
  }, [recommendation, unit])

  // Calculate progress percentage
  const oneRMProgress = useMemo(() => {
    if (!recommendation || lastOneRM === 0) return 0
    return calculateOneRMProgress(lastOneRM, currentOneRM)
  }, [recommendation, lastOneRM, currentOneRM])

  // Format progress message
  const progressMessage = useMemo(() => {
    if (!recommendation) return ''
    // Show 0% progress as well to indicate current 1RM matches previous
    if (oneRMProgress === 0) return '0%'
    return oneRMProgress >= 0 ? `+${oneRMProgress}%` : `${oneRMProgress}%`
  }, [recommendation, oneRMProgress])

  // Format last time info
  const lastTimeInfo = useMemo(() => {
    if (!recommendation) return ''

    const lastReps = recommendation.FirstWorkSetReps
    const lastWeight =
      unit === 'kg'
        ? recommendation.FirstWorkSetWeight?.Kg
        : recommendation.FirstWorkSetWeight?.Lb

    return `Last time: ${lastReps} x ${lastWeight} ${unit}`
  }, [recommendation, unit])

  // Handle weight change with validation
  const handleWeightChange = useCallback(
    (value: string) => {
      const numValue = parseFloat(value)
      if (Number.isNaN(numValue)) {
        return
      }

      onWeightChange?.(numValue)
    },
    [onWeightChange]
  )

  // Handle reps change with validation
  const handleRepsChange = useCallback(
    (value: string) => {
      const numValue = parseInt(value, 10)
      if (Number.isNaN(numValue)) return

      onRepsChange?.(numValue)
    },
    [onRepsChange]
  )

  return {
    currentOneRM,
    oneRMProgress,
    progressMessage,
    lastTimeInfo,
    handleWeightChange,
    handleRepsChange,
  }
}
