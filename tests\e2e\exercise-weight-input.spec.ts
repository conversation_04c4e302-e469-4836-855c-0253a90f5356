import { test, expect } from '@playwright/test'
import { login } from './helpers'

test.describe('Exercise Page Weight Input', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
    await login(page, '<EMAIL>', 'Dr123456')
  })

  test('should format weight with mobile app patterns', async ({ page }) => {
    // Navigate to workout
    await page.getByRole('link', { name: /workout/i }).click()

    // Wait for workout to load and click on first exercise
    await page.waitForSelector('[data-testid="exercise-card"]')
    await page.locator('[data-testid="exercise-card"]').first().click()

    // Wait for exercise page to load
    await page.waitForSelector('[data-testid="set-screen"]')

    // Get weight input
    const weightInput = page.getByRole('spinbutton', { name: 'Weight' })
    await expect(weightInput).toBeVisible()

    // Test typing decimal values
    await weightInput.clear()
    await weightInput.fill('100.999')

    // Value should be truncated to 2 decimal places when processed
    await weightInput.blur()

    // Test increment button
    const incrementButton = page.getByRole('button', {
      name: 'Increase weight',
    })
    await incrementButton.click()

    // Test decrement button
    const decrementButton = page.getByRole('button', {
      name: 'Decrease weight',
    })
    await decrementButton.click()
  })

  test('should handle weight increments with plate sizes', async ({ page }) => {
    // Navigate to workout
    await page.getByRole('link', { name: /workout/i }).click()

    // Wait for workout to load and click on first exercise
    await page.waitForSelector('[data-testid="exercise-card"]')
    await page.locator('[data-testid="exercise-card"]').first().click()

    // Wait for exercise page
    await page.waitForSelector('[data-testid="set-screen"]')

    // Get current weight value
    const weightInput = page.getByRole('spinbutton', { name: 'Weight' })
    const initialWeight = await weightInput.inputValue()

    // Click increment - should add 5lbs (or 2.5kg)
    const incrementButton = page.getByRole('button', {
      name: 'Increase weight',
    })
    await incrementButton.click()

    // Verify weight increased
    const newWeight = await weightInput.inputValue()
    expect(parseFloat(newWeight)).toBeGreaterThan(parseFloat(initialWeight))
  })

  test('should respect min/max weight bounds', async ({ page }) => {
    // Navigate to workout
    await page.getByRole('link', { name: /workout/i }).click()

    // Wait for workout to load and click on first exercise
    await page.waitForSelector('[data-testid="exercise-card"]')
    await page.locator('[data-testid="exercise-card"]').first().click()

    // Wait for exercise page
    await page.waitForSelector('[data-testid="set-screen"]')

    const weightInput = page.getByRole('spinbutton', { name: 'Weight' })
    const decrementButton = page.getByRole('button', {
      name: 'Decrease weight',
    })

    // Set weight to minimum (0)
    await weightInput.clear()
    await weightInput.fill('0')
    await weightInput.blur()

    // Try to decrement below 0
    await decrementButton.click()

    // Weight should still be 0
    const minWeight = await weightInput.inputValue()
    expect(parseFloat(minWeight)).toBeGreaterThanOrEqual(0)

    // Test maximum
    await weightInput.clear()
    await weightInput.fill('995')
    await weightInput.blur()

    const incrementButton = page.getByRole('button', {
      name: 'Increase weight',
    })

    // Increment twice should hit max of 1000
    await incrementButton.click()
    await incrementButton.click()

    const maxWeight = await weightInput.inputValue()
    expect(parseFloat(maxWeight)).toBeLessThanOrEqual(1000)
  })

  test('should handle bodyweight exercises', async ({ page }) => {
    // This would need a bodyweight exercise in the workout
    // Navigate to workout
    await page.getByRole('link', { name: /workout/i }).click()

    // Look for a bodyweight exercise (e.g., pull-ups, push-ups)
    const bodyweightExercise = page
      .locator('[data-testid="exercise-card"]', {
        hasText: /(pull.?up|push.?up|dip|chin.?up)/i,
      })
      .first()

    // If found, test it
    if (await bodyweightExercise.isVisible()) {
      await bodyweightExercise.click()
      await page.waitForSelector('[data-testid="set-screen"]')

      // Check for "Additional Weight" label
      const label = page.getByText('Additional Weight')
      if (await label.isVisible()) {
        expect(await label.isVisible()).toBe(true)

        // Bodyweight exercises can have 0 additional weight
        const weightInput = page.getByRole('spinbutton', { name: 'Weight' })
        await weightInput.clear()
        await weightInput.fill('0')
        await weightInput.blur()

        const weight = await weightInput.inputValue()
        expect(parseFloat(weight)).toBe(0)
      }
    }
  })

  test('should display weight with proper formatting', async ({ page }) => {
    // Navigate to workout
    await page.getByRole('link', { name: /workout/i }).click()

    // Wait for workout to load and click on first exercise
    await page.waitForSelector('[data-testid="exercise-card"]')
    await page.locator('[data-testid="exercise-card"]').first().click()

    // Wait for exercise page
    await page.waitForSelector('[data-testid="set-screen"]')

    const weightInput = page.getByRole('spinbutton', { name: 'Weight' })

    // Test integer input
    await weightInput.clear()
    await weightInput.fill('100')
    await weightInput.blur()
    let value = await weightInput.inputValue()
    expect(value).toBeTruthy()

    // Test decimal input
    await weightInput.clear()
    await weightInput.fill('100.5')
    await weightInput.blur()
    value = await weightInput.inputValue()
    expect(value).toBeTruthy()

    // Test precise decimal input
    await weightInput.clear()
    await weightInput.fill('100.25')
    await weightInput.blur()
    value = await weightInput.inputValue()
    expect(value).toBeTruthy()

    // Test trailing zeros (should be removed)
    await weightInput.clear()
    await weightInput.fill('100.00')
    await weightInput.blur()
    value = await weightInput.inputValue()
    expect(value).toBeTruthy()
  })
})
