import type {
  WorkoutLogSerieModelRef,
  MassUnit,
} from '@/types/api/WorkoutLogSerieModelRef'
import type { RecommendationModel, ExerciseModel } from '@/types/api'

export function createWarmupSet(
  warmup: { WarmUpReps: number; WarmUpWeightSet: { Kg: number; Lb: number } },
  index: number,
  exercise: ExerciseModel,
  recommendation: RecommendationModel,
  massUnit: MassUnit,
  totalWarmups: number
): WorkoutLogSerieModelRef {
  return {
    Id: -1000 - index,
    ExerciseId: exercise.Id,
    Reps: warmup.WarmUpReps,
    Weight: warmup.WarmUpWeightSet,
    IsWarmups: true,
    RIR: 0,
    IsAssisted: false,
    ExerciseName: exercise.Label,
    IsNext: false,
    IsFinished: false,
    IsActive: false,
    IsEditing: false,
    SetNo: (index + 1).toString(),
    SetTitle: `Warmup ${index + 1}`,
    IsLastSet: false,
    IsFirstSide: true,
    IsHeaderCell: false,
    IsFirstSetFinished: false,
    IsFirstWorkSet: false,
    IsLastWarmupSet: index === totalWarmups - 1,
    IsExerciseFinished: false,
    IsJustSetup: false,
    ShouldUpdateIncrement: false,
    NbPause: 0,
    OneRMProgress: 0,
    IsBackOffSet: false,
    IsNextBackOffSet: false,
    IsDropSet: false,
    IsNormalset: false,
    IsMaxChallenge: false,
    IsFlexibility: exercise.IsFlexibility || false,
    IsTimeBased: exercise.IsTimeBased || false,
    IsUnilateral: exercise.IsUnilateral || false,
    IsBodyweight: exercise.IsBodyweight || false,
    IsTimerOff: false,
    IsSizeChanged: false,
    ShowWorkTimer: false,
    LastTimeSet: '',
    PreviousReps: 0,
    PreviousWeight: { Kg: 0, Lb: 0 },
    Speed: 1.0,
    Increments: recommendation.Increments || { Kg: 2.5, Lb: 5 },
    Min: recommendation.Min || { Kg: 0, Lb: 0 },
    Max: recommendation.Max || { Kg: 200, Lb: 440 },
    HeaderImage: '',
    HeaderTitle: '',
    VideoUrl: exercise.VideoUrl || '',
    ShowPlusTooltip: false,
    ShowSuperSet3: false,
    ShowSuperSet2: false,
    get BackColor() {
      return 'transparent'
    },
    get WeightSingal() {
      if (!this.Weight) return '0.00'
      return massUnit === 'kg'
        ? (this.Weight.Kg || 0).toFixed(2)
        : (this.Weight.Lb || 0).toFixed(2)
    },
    get WeightDouble() {
      if (!this.Weight) return '0.00'
      return massUnit === 'kg'
        ? (this.Weight.Kg || 0).toFixed(2)
        : (this.Weight.Lb || 0).toFixed(2)
    },
  }
}

export function createWorkSet(
  index: number,
  exercise: ExerciseModel,
  recommendation: RecommendationModel,
  massUnit: MassUnit,
  numWorkSets: number
): WorkoutLogSerieModelRef {
  const isFirst = index === 0
  const isLast = index === numWorkSets - 1

  return {
    Id: index + 1,
    ExerciseId: exercise.Id,
    Reps: recommendation.Reps,
    Weight: recommendation.Weight,
    IsWarmups: false,
    RIR: recommendation.RIR || 2,
    IsAssisted: false,
    ExerciseName: exercise.Label,
    IsNext: isFirst,
    IsFinished: false,
    IsActive: false,
    IsEditing: false,
    SetNo: (index + 1).toString(),
    SetTitle: `Set ${index + 1}`,
    IsLastSet: isLast,
    IsFirstSide: true,
    IsHeaderCell: false,
    IsFirstSetFinished: false,
    IsFirstWorkSet: isFirst,
    IsLastWarmupSet: false,
    IsExerciseFinished: false,
    IsJustSetup: false,
    ShouldUpdateIncrement: false,
    NbPause: 0,
    OneRMProgress: 0,
    IsBackOffSet: recommendation.IsBackOffSet || false,
    IsNextBackOffSet: false,
    IsDropSet: recommendation.IsDropSet || false,
    IsNormalset: recommendation.IsNormalSets || false,
    IsMaxChallenge: recommendation.IsMaxChallenge || false,
    IsFlexibility: exercise.IsFlexibility || false,
    IsTimeBased: exercise.IsTimeBased || false,
    IsUnilateral: exercise.IsUnilateral || false,
    IsBodyweight: exercise.IsBodyweight || false,
    IsTimerOff: false,
    IsSizeChanged: false,
    ShowWorkTimer: true,
    LastTimeSet: recommendation.LastLogDate
      ? new Date(recommendation.LastLogDate).toLocaleDateString()
      : '',
    PreviousReps: recommendation.HistorySet?.[0]?.Reps || 0,
    PreviousWeight: recommendation.HistorySet?.[0]?.Weight || {
      Kg: 0,
      Lb: 0,
    },
    Speed: recommendation.Speed || 1.0,
    Increments: recommendation.Increments || { Kg: 2.5, Lb: 5 },
    Min: recommendation.Min || { Kg: 0, Lb: 0 },
    Max: recommendation.Max || { Kg: 200, Lb: 440 },
    HeaderImage: '',
    HeaderTitle: '',
    VideoUrl: exercise.VideoUrl || '',
    ShowPlusTooltip: false,
    ShowSuperSet3: false,
    ShowSuperSet2: false,
    get BackColor() {
      if (this.IsFinished || this.IsNext) {
        return '#4D0C2432'
      }
      return 'transparent'
    },
    get WeightSingal() {
      if (!this.Weight) return '0.00'
      return massUnit === 'kg'
        ? (this.Weight.Kg || 0).toFixed(2)
        : (this.Weight.Lb || 0).toFixed(2)
    },
    get WeightDouble() {
      if (!this.Weight) return '0.00'
      return massUnit === 'kg'
        ? (this.Weight.Kg || 0).toFixed(2)
        : (this.Weight.Lb || 0).toFixed(2)
    },
  }
}
