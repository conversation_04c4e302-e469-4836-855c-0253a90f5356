module.exports = {
  extends: [
    'airbnb',
    'airbnb-typescript',
    'plugin:@typescript-eslint/recommended',
    'plugin:prettier/recommended',
    'next/core-web-vitals'
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
    project: './tsconfig.eslint.json',
  },
  overrides: [
    {
      files: ['**/*.test.ts', '**/*.test.tsx', '**/*.spec.ts', '**/*.spec.tsx'],
      rules: {
        '@typescript-eslint/no-explicit-any': 'off',
        'import/extensions': 'off',
      },
    },
    {
      files: ['**/*.example.ts', '**/*.example.tsx'],
      rules: {
        'no-console': 'off',
      },
    },
    {
      files: ['src/api/workouts.ts'],
      rules: {
        'no-await-in-loop': 'off',
      },
    },
    {
      files: ['tests/e2e/helpers/webkit-stability.ts'],
      rules: {
        'no-await-in-loop': 'off',
        'no-console': 'off',
      },
    },
  ],
  plugins: ['react', '@typescript-eslint', 'prettier'],
  rules: {
    'react/react-in-jsx-scope': 'off',
    'react/prop-types': 'off',
    'react/jsx-props-no-spreading': 'off',
    'import/prefer-default-export': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/naming-convention': 'off',
    '@typescript-eslint/no-shadow': 'off',
    '@typescript-eslint/dot-notation': 'off',
    '@typescript-eslint/return-await': 'off',
    'no-console': ['warn', { allow: ['warn', 'error'] }],
    'prettier/prettier': ['error', { endOfLine: 'auto' }],
    'no-underscore-dangle': 'off',
    'no-param-reassign': 'off',
    'no-plusplus': 'off',
    'prefer-exponentiation-operator': 'off',
    'no-restricted-properties': 'off',
    'import/order': 'off',
    'jsx-a11y/label-has-associated-control': 'off',
    'react/button-has-type': 'off',
    'react/no-unescaped-entities': 'off',
    'no-else-return': 'off',
    'react/require-default-props': 'off',
    'jsx-a11y/click-events-have-key-events': 'off',
    'jsx-a11y/no-static-element-interactions': 'off',
    'jsx-a11y/no-noninteractive-element-interactions': 'off',
    'jsx-a11y/control-has-associated-label': 'off',
    'radix': 'off',
    'consistent-return': 'off',
    'react/display-name': 'off',
    'func-names': 'off',
    'no-unsafe-optional-chaining': 'off',
    'no-promise-executor-return': 'off',
  },
}