import { render, screen } from '@testing-library/react'
import { vi } from 'vitest'
import { ExerciseSwapModal } from '../ExerciseSwapModal'
import type { ExerciseModel, WorkoutTemplateModel } from '@/types'

// Mock the API and services
vi.mock('@/api/exercise/search')
vi.mock('@/services/swapExerciseService')
vi.mock('@/utils/logger')

describe('ExerciseSwapModal', () => {
  const mockExercise: ExerciseModel = {
    Id: 1,
    Label: 'Bench Press',
    BodyPartId: 1,
    IsBodyweight: false,
  }

  const mockWorkout: WorkoutTemplateModel = {
    Id: 1,
    Label: 'Chest Day',
    Exercises: [mockExercise],
  }

  const mockOnSwap = vi.fn()
  const mockOnClose = vi.fn()

  const defaultProps = {
    isOpen: true,
    currentExercise: mockExercise,
    workout: mockWorkout,
    onSwap: mockOnSwap,
    onClose: mockOnClose,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('z-index hierarchy', () => {
    it('should have a higher z-index than FloatingCTAButton (z-50)', () => {
      render(<ExerciseSwapModal {...defaultProps} />)

      // Get the modal backdrop element
      const modalBackdrop = screen.getByRole('presentation')

      // Check that it has a z-index class higher than z-50
      expect(modalBackdrop).toHaveClass('z-[60]')

      // Ensure it doesn't have the old z-50 class
      expect(modalBackdrop).not.toHaveClass('z-50')
    })

    it('should render above other fixed elements when open', () => {
      render(<ExerciseSwapModal {...defaultProps} />)

      const modalBackdrop = screen.getByRole('presentation')

      // Check that modal has fixed positioning with high z-index
      expect(modalBackdrop).toHaveClass('fixed')
      expect(modalBackdrop).toHaveClass('inset-0')
      expect(modalBackdrop).toHaveClass('z-[60]')
    })
  })

  describe('modal behavior', () => {
    it('should not render when isOpen is false', () => {
      render(<ExerciseSwapModal {...defaultProps} isOpen={false} />)

      const modalDialog = screen.queryByRole('dialog')
      expect(modalDialog).not.toBeInTheDocument()
    })

    it('should render modal dialog when isOpen is true', () => {
      render(<ExerciseSwapModal {...defaultProps} />)

      const modalDialog = screen.getByRole('dialog', { name: 'Swap Exercise' })
      expect(modalDialog).toBeInTheDocument()
    })

    it('should display current exercise name', () => {
      render(<ExerciseSwapModal {...defaultProps} />)

      expect(screen.getByText('Bench Press')).toBeInTheDocument()
      expect(screen.getByText(/Replace/)).toBeInTheDocument()
    })
  })
})
