# Enhanced Exercise Swap Feature

The exercise swap feature allows users to replace exercises in their workouts with alternatives, either temporarily (for the current session only) or permanently (saved to their workout template).

## Overview

The feature provides two swap modes:

1. **Temporary Swaps**: Changes are saved locally and only apply to the current workout session
2. **Permanent Swaps**: Changes are saved to the workout template in the database and apply to all future instances of the workout

## Architecture

### Core Components

- **SwapExerciseService**: Main service handling both temporary and permanent swaps
- **ExerciseSwapModal**: UI component for selecting exercises and swap type
- **ExerciseCard**: Exercise display component with swap button
- **WorkoutStore**: State management for swap contexts

### Data Flow

1. User clicks swap button on an exercise
2. ExerciseSwapModal opens with exercise alternatives
3. User selects new exercise and swap type (temporary/permanent)
4. SwapExerciseService processes the swap:
   - **Temporary**: Saves to localStorage
   - **Permanent**: Calls workout template API to modify the workout
5. UI updates to reflect the change

## Usage

### For Temporary Swaps

```typescript
const result = await swapExerciseService.swapExercise(
  workout,
  sourceExerciseId,
  targetExercise,
  { temporary: true, permanent: false }
)

// result.savedToDb will be false
// Change is applied locally only
```

### For Permanent Swaps

```typescript
const result = await swapExerciseService.swapExercise(
  workout,
  sourceExerciseId,
  targetExercise,
  { temporary: false, permanent: true }
)

// result.savedToDb will be true
// Change is saved to workout template
```

## API Endpoints

### Permanent Swaps

- **System Workouts**: `POST /api/Workout/CreateNewUserWorkoutTemplate`
  - Creates a user copy of the system workout with modifications
- **User Workouts**: `POST /api/Workout/CreateNewWorkoutTemplate`
  - Updates the existing user workout template

### Exercise Alternatives

- `GET /api/Exercise/GetByBodyPart/{bodyPartId}` - Get exercises for swap selection

## Storage

### Temporary Swaps

- Stored in `localStorage` under key `temp_swap_exercise_contexts`
- Persists across page refreshes within the same session
- Automatically applied when loading workouts

### Permanent Swaps

- Stored in workout template on the server
- Applied automatically when workout is loaded
- Persists across devices and sessions

## UI Components

### ExerciseSwapModal

```typescript
interface ExerciseSwapModalProps {
  isOpen: boolean
  currentExercise: ExerciseModel
  workout: WorkoutTemplateModel
  onSwap: (result: ExerciseSwapResult) => void
  onClose: () => void
}
```

Features:

- Exercise selection with body part prioritization
- Radio buttons for temporary vs permanent swap
- Loading states and error handling
- Responsive design

### ExerciseCard Integration

- Swap button with exchange icon
- "Swapped" indicator for modified exercises
- Handles both temporary and permanent swap results

## Error Handling

- Network errors for permanent swaps
- localStorage errors for temporary swaps
- Exercise not found errors
- API authentication errors

## Testing

- Unit tests for SwapExerciseService
- Integration tests for UI components
- E2E tests for complete swap workflow

## Future Enhancements

- Bulk swap operations
- Swap history and analytics
- Exercise recommendation improvements based on swaps
- Offline sync for permanent swaps
