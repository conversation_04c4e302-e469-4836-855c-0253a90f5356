How Sets are Saved in the Dr. Muscle MAUI Mobile App
Overview
The MAUI mobile app saves workout sets through a comprehensive process that involves both individual set saving during workouts and final workout completion. Here's a detailed explanation of how this works:

Architecture Components

1. Data Models
   csharp

// WorkoutLogSerieModel.cs
public class WorkoutLogSerieModel : BaseModel, IDisposable
{
public long Id { get; set; }
public ExerciceModel Exercice { get; set; }
public long? BodypartId { get; set; }
public string UserId { get; set; }
public DateTime LogDate { get; set; }
public int Reps { get; set; }
public MultiUnityWeight Weight { get; set; }
public MultiUnityWeight OneRM { get; set; }
public bool IsWarmups { get; set; }
public bool Isbodyweight { get; set; }
public bool IsPlateAvailable { get; set; }
public bool IsDumbbellAvailable { get; set; }
public bool IsBandsAvailable { get; set; }
public bool IsPulleyAvailable { get; set; }
public int? NbPause { get; set; }
public int? RIR { get; set; } // Reps In Reserve
public bool IsOneHanded { get; set; }
public bool IsReferenceSet { get; set; }
}

// SaveWorkoutModel.cs
public class SaveWorkoutModel
{
public long WorkoutId { get; set; }
public long WorkoutTemplateId { get; set; }
} 2. Set Saving Process
The mobile app saves sets in two stages:

Stage 1: Individual Set Saving (During Exercise)
From SaveSetPage.xaml.cs:

csharp

private async void SaveSet_Clicked(object sender, EventArgs e)
{
if (currentWeight > 0 || currentReps > 0)
{
// Create the set model
WorkoutLogSerieModelEx serieModel = new WorkoutLogSerieModelEx()
{
Exercice = new ExerciceModel() { Id = CurrentLog.Instance.ExerciseLog.Exercice.Id },
Reps = currentReps,
UserId = CurrentLog.Instance.ExerciseLog.UserId,
Weight = new MultiUnityWeight(currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value),
RIR = RIR,
IsWarmups = workoutLogSerieModel.Count < reco.WarmUpsList.Count ? true : false
};

        // Add to local collection
        workoutLogSerieModel.Add(serieModel);

        // Update UI and start rest timer
        UpdateTopLabels();
        Timer.Instance.StartTimer();
    }

}
Stage 2: Finishing an Exercise (Save All Sets)
From SaveSetPage.xaml.cs - PushToDataServer() method:

csharp

private async void PushToDataServer()
{
bool result = true;
try
{
// Save each set to the backend
foreach (WorkoutLogSerieModel l in workoutLogSerieModel)
{
BooleanModel b = await DrMuscleRestClient.Instance.AddWorkoutLogSerie(l);
result = result && b.Result;
}

        // Update exercise session data
        await DrMuscleRestClient.Instance.AddUpdateExerciseUserLightSession(new LightSessionModel()
        {
            ExerciseId = CurrentLog.Instance.ExerciseLog.Exercice.Id,
            IsLightSession = CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].IsLightSession,
            MaxChallengeDate = maxDate
        });

        // Clear local data after successful save
        CurrentLog.Instance.WorkoutLogSeriesByExercise.Remove(CurrentLog.Instance.ExerciseLog.Exercice.Id);
        await PagesFactory.PushAsync<EndExercisePage>();
    }
    catch (Exception ex)
    {
        // Handle errors
    }

} 3. API Endpoints Used
From DrMuscleRestClient.cs:

csharp

// Save individual sets
public async Task<BooleanModel> AddWorkoutLogSerie(WorkoutLogSerieModel model)
{
return await PostJson<BooleanModel>("api/Exercise/AddWorkoutLogSerieNew", model);
}

// Save multiple sets at once (batch operation)
public async Task<BooleanModel> AddWorkoutLogSerieList(List<WorkoutLogSerieModel> model)
{
return await PostJson<BooleanModel>("api/Exercise/AddWorkoutLogSerieListNew", model);
}

// Save the entire workout (called when finishing the workout)
public async Task<BooleanModel> SaveWorkout(SaveWorkoutModel model)
{
return await PostJson<BooleanModel>("api/Workout/SaveWorkoutPro", model);
}

// Save workout and get updated info
public async Task<GetUserProgramInfoResponseModel> SaveGetWorkoutInfo(SaveWorkoutModel model)
{
return await PostJson<GetUserProgramInfoResponseModel>("api/Workout/SaveGetWorkoutInfoPro", model);
} 4. Workout Completion Process
From KenkoChooseYourWorkoutExercisePage.xaml.cs:

csharp

private async void SaveWorkoutButton_Clicked(object sender, EventArgs e)
{
// Save all remaining sets
foreach (var item in workoutLogSerieModelRef)
{
WorkoutLogSerieModelEx serieModel = new WorkoutLogSerieModelEx()
{
Exercice = new ExerciceModel() { Id = model.Id },
Reps = l.Reps,
UserId = CurrentLog.Instance.ExerciseLog.UserId,
Weight = new MultiUnityWeight(weightDouble, isKg ? "kg" : "lb"),
RIR = RIR,
IsWarmups = l.IsWarmups,
OneRM = new MultiUnityWeight(ComputeOneRM(...), "kg")
};

        workoutLogSerieModel.Add(serieModel);
    }

    // Save all sets to backend
    BooleanModel successLog = await DrMuscleRestClient.Instance.AddWorkoutLogSerieListwithoutLoader(workoutLogSerieModel);

    // Mark workout as complete
    var responseLog = await DrMuscleRestClient.Instance.SaveGetWorkoutInfo(
        new SaveWorkoutModel() { WorkoutId = CurrentLog.Instance.CurrentWorkoutTemplate.Id }
    );

    // Update local storage
    LocalDBManager.Instance.SetDBSetting("last_workout_label", CurrentLog.Instance.CurrentWorkoutTemplate.Label);
    LocalDBManager.Instance.SetDBSetting("lastDoneProgram", CurrentLog.Instance.CurrentWorkoutTemplate.Id.ToString());

}
Step-by-Step Implementation Plan for Web App
Phase 1: Data Models and Types
Create TypeScript interfaces matching the mobile app models:
typescript

// types/workout.ts
export interface WorkoutLogSerieModel {
id?: number;
exercice: ExerciseModel;
bodypartId?: number;
userId: string;
logDate: Date;
reps: number;
weight: MultiUnityWeight;
oneRM?: MultiUnityWeight;
isWarmups: boolean;
isbodyweight: boolean;
isPlateAvailable?: boolean;
isDumbbellAvailable?: boolean;
isBandsAvailable?: boolean;
isPulleyAvailable?: boolean;
nbPause?: number;
RIR?: number;
isOneHanded: boolean;
isReferenceSet?: boolean;
}

export interface SaveWorkoutModel {
workoutId: number;
workoutTemplateId?: number;
}

export interface MultiUnityWeight {
kg: number;
lb: number;
}
Phase 2: State Management
Create a workout context/store to manage sets during workout:
typescript

// store/workoutStore.ts
import { create } from 'zustand';

interface WorkoutStore {
workoutLogSeriesByExercise: Map<number, WorkoutLogSerieModel[]>;
currentExerciseId: number | null;

addSet: (exerciseId: number, set: WorkoutLogSerieModel) => void;
clearExerciseSets: (exerciseId: number) => void;
getExerciseSets: (exerciseId: number) => WorkoutLogSerieModel[];
}

export const useWorkoutStore = create<WorkoutStore>((set, get) => ({
workoutLogSeriesByExercise: new Map(),
currentExerciseId: null,

addSet: (exerciseId, setData) => {
const current = get().workoutLogSeriesByExercise;
const exerciseSets = current.get(exerciseId) || [];
exerciseSets.push(setData);
current.set(exerciseId, exerciseSets);
set({ workoutLogSeriesByExercise: new Map(current) });
},

clearExerciseSets: (exerciseId) => {
const current = get().workoutLogSeriesByExercise;
current.delete(exerciseId);
set({ workoutLogSeriesByExercise: new Map(current) });
},

getExerciseSets: (exerciseId) => {
return get().workoutLogSeriesByExercise.get(exerciseId) || [];
}
}));
Phase 3: API Service Layer
Create API service functions:
typescript

// services/workoutApi.ts
import { api } from '@/lib/api';

export const workoutApi = {
// Save individual set
async addWorkoutLogSerie(set: WorkoutLogSerieModel): Promise<boolean> {
const response = await api.post('/api/Exercise/AddWorkoutLogSerieNew', set);
return response.data.result;
},

// Save multiple sets (batch)
async addWorkoutLogSerieList(sets: WorkoutLogSerieModel[]): Promise<boolean> {
const response = await api.post('/api/Exercise/AddWorkoutLogSerieListNew', sets);
return response.data.result;
},

// Complete and save workout
async saveWorkout(model: SaveWorkoutModel): Promise<boolean> {
const response = await api.post('/api/Workout/SaveWorkoutPro', model);
return response.data.result;
},

// Save workout and get updated info
async saveGetWorkoutInfo(model: SaveWorkoutModel): Promise<any> {
const response = await api.post('/api/Workout/SaveGetWorkoutInfoPro', model);
return response.data;
}
};
Phase 4: UI Components
Create set tracking component:
typescript

// components/workout/SetTracker.tsx
export function SetTracker({ exercise, recommendation }) {
const { addSet } = useWorkoutStore();
const [weight, setWeight] = useState(recommendation.weight);
const [reps, setReps] = useState(recommendation.reps);
const [RIR, setRIR] = useState<number | null>(null);

const handleSaveSet = () => {
const set: WorkoutLogSerieModel = {
exercice: { id: exercise.id },
reps,
weight,
userId: getCurrentUserId(),
logDate: new Date(),
RIR,
isWarmups: false,
isbodyweight: exercise.isBodyweight,
isOneHanded: exercise.isUnilateral,
oneRM: calculateOneRM(weight, reps)
};

    addSet(exercise.id, set);
    // Start rest timer
    startRestTimer();

};

return (

<div>
<WeightInput value={weight} onChange={setWeight} />
<RepsInput value={reps} onChange={setReps} />
<Button onClick={handleSaveSet}>Save Set</Button>
</div>
);
}
Create exercise completion handler:
typescript

// components/workout/ExerciseActions.tsx
export function ExerciseActions({ exercise }) {
const { getExerciseSets, clearExerciseSets } = useWorkoutStore();
const [isSaving, setIsSaving] = useState(false);

const handleFinishExercise = async () => {
setIsSaving(true);
try {
const sets = getExerciseSets(exercise.id);

      // Save all sets for this exercise
      const success = await workoutApi.addWorkoutLogSerieList(sets);

      if (success) {
        // Clear local sets after successful save
        clearExerciseSets(exercise.id);

        // Navigate to next exercise or workout summary
        router.push('/workout/next-exercise');
      }
    } catch (error) {
      console.error('Failed to save exercise:', error);
    } finally {
      setIsSaving(false);
    }

};

return (
<Button onClick={handleFinishExercise} disabled={isSaving}>
Finish Exercise
</Button>
);
}
Phase 5: Workout Completion
Create workout completion handler:
typescript

// components/workout/WorkoutSummary.tsx
export function WorkoutSummary({ workoutId, exercises }) {
const { workoutLogSeriesByExercise } = useWorkoutStore();

const handleSaveWorkout = async () => {
try {
// Save any remaining unsaved sets
for (const [exerciseId, sets] of workoutLogSeriesByExercise) {
if (sets.length > 0) {
await workoutApi.addWorkoutLogSerieList(sets);
}
}

      // Mark workout as complete
      const result = await workoutApi.saveGetWorkoutInfo({
        workoutId: workoutId
      });

      // Update local storage
      localStorage.setItem('last_workout_label', result.workoutLabel);
      localStorage.setItem('lastDoneProgram', workoutId.toString());

      // Navigate to success page
      router.push('/workout/completed');
    } catch (error) {
      console.error('Failed to save workout:', error);
    }

};

return (

<div>
<h2>Workout Summary</h2>
<Button onClick={handleSaveWorkout}>Finish & Save Workout</Button>
</div>
);
}
Phase 6: Error Handling and Offline Support
Add error handling and retry logic:
typescript

// utils/workoutSync.ts
export class WorkoutSyncManager {
private pendingSets: WorkoutLogSerieModel[] = [];

async saveSetsWithRetry(sets: WorkoutLogSerieModel[], maxRetries = 3) {
let retries = 0;

    while (retries < maxRetries) {
      try {
        const result = await workoutApi.addWorkoutLogSerieList(sets);
        if (result) {
          return true;
        }
      } catch (error) {
        retries++;
        if (retries === maxRetries) {
          // Store for later sync
          this.pendingSets.push(...sets);
          localStorage.setItem('pendingSets', JSON.stringify(this.pendingSets));
          throw error;
        }
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 1000 * retries));
      }
    }
    return false;

}

async syncPendingSets() {
const pending = JSON.parse(localStorage.getItem('pendingSets') || '[]');
if (pending.length > 0) {
try {
await workoutApi.addWorkoutLogSerieList(pending);
localStorage.removeItem('pendingSets');
} catch (error) {
console.error('Failed to sync pending sets:', error);
}
}
}
}
Key Implementation Considerations
Authentication: Ensure all API calls include the user's auth token
Data Validation: Validate weight/reps before saving
Unit Conversion: Handle kg/lb conversions properly
One-Rep Max Calculation: Implement the same formula as the mobile app
Rest Timer Integration: Start rest timer after each set
RIR (Reps in Reserve): Collect after first work set
Warmup Detection: Mark sets as warmups based on recommendation
Error Recovery: Handle network failures gracefully
Progress Indicators: Show saving status to users
Unilateral Exercises: Handle side-specific tracking for unilateral exercises
Testing Strategy
Test individual set saving
Test batch set saving for exercises
Test workout completion flow
Test error scenarios (network failures)
Test data persistence across app refreshes
Test unit conversions
Test warmup vs work set detection
This implementation ensures the web app saves workout data in the same manner as the mobile app, maintaining consistency across platforms.
