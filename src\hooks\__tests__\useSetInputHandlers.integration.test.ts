import { renderHook, act } from '@testing-library/react'
import { describe, expect, it, vi } from 'vitest'
import { useSetInputHandlers } from '../useSetInputHandlers'

describe('useSetInputHandlers Integration Tests', () => {
  describe('Weight increment/decrement with roundToNearestIncrement', () => {
    it('should round weight to nearest 5lb increment when incrementing', () => {
      const onChange = vi.fn()
      const { result } = renderHook(() =>
        useSetInputHandlers({
          reps: 10,
          weight: 97.5,
          duration: 0,
          unit: 'lbs',
          onChange,
        })
      )

      act(() => {
        result.current.incrementWeight()
      })

      // 97.5 + 5 = 102.5, which rounds to 105 (nearest 5)
      expect(onChange).toHaveBeenCalledWith({
        reps: 10,
        weight: 105,
      })
    })

    it('should round weight to nearest 2.5kg increment when incrementing', () => {
      const onChange = vi.fn()
      const { result } = renderHook(() =>
        useSetInputHandlers({
          reps: 10,
          weight: 42.5,
          duration: 0,
          unit: 'kg',
          onChange,
        })
      )

      act(() => {
        result.current.incrementWeight()
      })

      // 42.5 + 2.5 = 45
      expect(onChange).toHaveBeenCalledWith({
        reps: 10,
        weight: 45,
      })
    })

    it('should respect minimum weight of 0 when decrementing', () => {
      const onChange = vi.fn()
      const { result } = renderHook(() =>
        useSetInputHandlers({
          reps: 10,
          weight: 2.5,
          duration: 0,
          unit: 'lbs',
          onChange,
        })
      )

      act(() => {
        result.current.decrementWeight()
      })

      // 2.5 - 5 = -2.5, which should be clamped to 0
      expect(onChange).toHaveBeenCalled()
      const call = onChange.mock.calls[0][0]
      expect(call.reps).toBe(10)
      expect(Math.abs(call.weight)).toBe(0) // Handle -0 case
    })

    it('should respect maximum weight of 1000 when incrementing', () => {
      const onChange = vi.fn()
      const { result } = renderHook(() =>
        useSetInputHandlers({
          reps: 10,
          weight: 997.5,
          duration: 0,
          unit: 'lbs',
          onChange,
        })
      )

      act(() => {
        result.current.incrementWeight()
      })

      // 997.5 + 5 = 1002.5, which should be clamped to 1000
      expect(onChange).toHaveBeenCalledWith({
        reps: 10,
        weight: 1000,
      })
    })
  })

  describe('Weight input with truncateDecimal', () => {
    it('should truncate weight to 2 decimal places when typing', () => {
      const onChange = vi.fn()
      const { result } = renderHook(() =>
        useSetInputHandlers({
          reps: 10,
          weight: 100,
          duration: 0,
          unit: 'lbs',
          onChange,
        })
      )

      act(() => {
        result.current.handleWeightChange('100.999')
      })

      // 100.999 should be truncated to 100.99
      expect(onChange).toHaveBeenCalledWith({
        reps: 10,
        weight: 100.99,
      })
    })

    it('should handle very small decimal values', () => {
      const onChange = vi.fn()
      const { result } = renderHook(() =>
        useSetInputHandlers({
          reps: 10,
          weight: 0,
          duration: 0,
          unit: 'kg',
          onChange,
        })
      )

      act(() => {
        result.current.handleWeightChange('0.123456')
      })

      // 0.123456 should be truncated to 0.12
      expect(onChange).toHaveBeenCalledWith({
        reps: 10,
        weight: 0.12,
      })
    })

    it('should preserve valid decimal values', () => {
      const onChange = vi.fn()
      const { result } = renderHook(() =>
        useSetInputHandlers({
          reps: 10,
          weight: 100,
          duration: 0,
          unit: 'lbs',
          onChange,
        })
      )

      act(() => {
        result.current.handleWeightChange('102.5')
      })

      // 102.5 should remain 102.5
      expect(onChange).toHaveBeenCalledWith({
        reps: 10,
        weight: 102.5,
      })
    })
  })

  describe('Time-based exercises', () => {
    it('should handle weight changes for time-based exercises', () => {
      const onChange = vi.fn()
      const { result } = renderHook(() =>
        useSetInputHandlers({
          reps: undefined,
          weight: 50,
          duration: 30,
          unit: 'kg',
          isTimeBased: true,
          onChange,
        })
      )

      act(() => {
        result.current.incrementWeight()
      })

      // 50 + 2.5 = 52.5
      expect(onChange).toHaveBeenCalledWith({
        weight: 52.5,
        duration: 30,
      })
    })
  })

  describe('Bodyweight exercises', () => {
    it('should allow incrementing from 0 for bodyweight exercises', () => {
      const onChange = vi.fn()
      const { result } = renderHook(() =>
        useSetInputHandlers({
          reps: 10,
          weight: 0,
          duration: 0,
          unit: 'lbs',
          isBodyweight: true,
          onChange,
        })
      )

      act(() => {
        result.current.incrementWeight()
      })

      // 0 + 5 = 5
      expect(onChange).toHaveBeenCalledWith({
        reps: 10,
        weight: 5,
      })
    })

    it('should allow decrementing to 0 for bodyweight exercises', () => {
      const onChange = vi.fn()
      const { result } = renderHook(() =>
        useSetInputHandlers({
          reps: 10,
          weight: 5,
          duration: 0,
          unit: 'lbs',
          isBodyweight: true,
          onChange,
        })
      )

      act(() => {
        result.current.decrementWeight()
      })

      // 5 - 5 = 0
      expect(onChange).toHaveBeenCalledWith({
        reps: 10,
        weight: 0,
      })
    })
  })
})
