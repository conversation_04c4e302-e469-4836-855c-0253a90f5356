import { describe, it, expect } from 'vitest'
import { isValidExerciseId } from '../exerciseValidation'

describe('isValidExerciseId', () => {
  it('should return true for valid positive exercise IDs', () => {
    expect(isValidExerciseId(1)).toBe(true)
    expect(isValidExerciseId(100)).toBe(true)
    expect(isValidExerciseId(12980)).toBe(true)
    expect(isValidExerciseId(999999)).toBe(true)
  })

  it('should return false for zero', () => {
    expect(isValidExerciseId(0)).toBe(false)
  })

  it('should return false for negative numbers', () => {
    expect(isValidExerciseId(-1)).toBe(false)
    expect(isValidExerciseId(-100)).toBe(false)
  })

  it('should return false for null', () => {
    expect(isValidExerciseId(null)).toBe(false)
  })

  it('should return false for undefined', () => {
    expect(isValidExerciseId(undefined)).toBe(false)
  })

  it('should return false for NaN', () => {
    expect(isValidExerciseId(NaN)).toBe(false)
  })

  it('should return false for non-number types', () => {
    expect(isValidExerciseId('123' as any)).toBe(false)
    expect(isValidExerciseId('' as any)).toBe(false)
    expect(isValidExerciseId({} as any)).toBe(false)
    expect(isValidExerciseId([] as any)).toBe(false)
    expect(isValidExerciseId(true as any)).toBe(false)
  })

  it('should return false for Infinity', () => {
    expect(isValidExerciseId(Infinity)).toBe(false)
    expect(isValidExerciseId(-Infinity)).toBe(false)
  })

  it('should return false for decimal numbers', () => {
    expect(isValidExerciseId(1.5)).toBe(false)
    expect(isValidExerciseId(10.1)).toBe(false)
  })
})
