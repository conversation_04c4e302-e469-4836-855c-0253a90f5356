import { renderHook, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { useAuth } from '../useAuth'
import { authApi } from '@/api/auth'
import { userProfileApi } from '@/api/userProfile'
import { programApi } from '@/api/program'
import { createQueryWrapper } from '../../../tests/test-utils'
import { debugLog } from '@/utils/debugLog'

vi.mock('@/api/auth')
vi.mock('@/api/userProfile')
vi.mock('@/api/program')
vi.mock('@/utils/userInfoPerformance', () => ({
  startSession: vi.fn(() => 'session-id'),
  endSession: vi.fn(),
}))
vi.mock('@/utils/debugLog')

describe('useAuth root cause fixes', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should prefetch user info during login but handle 404 errors gracefully', async () => {
    // Given - successful login but 404 on user info (new user)
    const loginResponse = {
      Token: 'test-token',
      RefreshToken: 'refresh-token',
      user: { id: '123', email: '<EMAIL>' },
    }
    vi.mocked(authApi.login).mockResolvedValue(loginResponse)
    vi.mocked(userProfileApi.getUserInfo).mockRejectedValue({
      response: { status: 404 },
      message: 'Not found',
    })

    // When - login
    const { result } = renderHook(() => useAuth(), {
      wrapper: createQueryWrapper(),
    })

    await result.current.login({
      Username: '<EMAIL>',
      Password: 'password',
    })

    // Wait for prefetch to complete
    await waitFor(() => {
      expect(vi.mocked(userProfileApi.getUserInfo)).toHaveBeenCalled()
    })

    // Then - should attempt to fetch user info during login
    expect(vi.mocked(userProfileApi.getUserInfo)).toHaveBeenCalled()

    // But no errors should be logged for 404
    expect(vi.mocked(debugLog.warn)).not.toHaveBeenCalled()
    expect(vi.mocked(debugLog.error)).not.toHaveBeenCalled()

    // Login should still succeed
    expect(result.current.isAuthenticated).toBe(true)
  })

  it('should prefetch program data during login but handle missing data gracefully', async () => {
    // Given - successful login but no program data
    const loginResponse = {
      Token: 'test-token',
      RefreshToken: 'refresh-token',
      user: { id: '123', email: '<EMAIL>' },
    }
    vi.mocked(authApi.login).mockResolvedValue(loginResponse)
    vi.mocked(userProfileApi.getUserInfo).mockResolvedValue({
      Email: '<EMAIL>',
      FirstName: 'Test',
      LastName: 'User',
    })
    vi.mocked(programApi.getUserProgram).mockResolvedValue(null)

    // When - login
    const { result } = renderHook(() => useAuth(), {
      wrapper: createQueryWrapper(),
    })

    await result.current.login({
      Username: '<EMAIL>',
      Password: 'password',
    })

    // Wait for prefetch to complete
    await waitFor(() => {
      expect(vi.mocked(programApi.getUserProgram)).toHaveBeenCalled()
    })

    // Then - should attempt to fetch program data during login
    expect(vi.mocked(programApi.getUserProgram)).toHaveBeenCalled()

    // But not progress/stats (those are fetched later)
    expect(vi.mocked(programApi.getProgramProgress)).not.toHaveBeenCalled()
    expect(vi.mocked(programApi.getProgramStats)).not.toHaveBeenCalled()

    // No errors should be logged for missing data
    expect(vi.mocked(debugLog.warn)).not.toHaveBeenCalled()
    expect(vi.mocked(debugLog.error)).not.toHaveBeenCalled()
  })

  it('should allow manual user info fetch when needed', async () => {
    // Given - user is already logged in
    const loginResponse = {
      Token: 'test-token',
      RefreshToken: 'refresh-token',
      user: { id: '123', email: '<EMAIL>' },
    }
    vi.mocked(authApi.login).mockResolvedValue(loginResponse)

    const userInfo = {
      Email: '<EMAIL>',
      FirstName: 'Test',
      LastName: 'User',
    }
    vi.mocked(userProfileApi.getUserInfo).mockResolvedValue(userInfo)

    // When - login first
    const { result } = renderHook(() => useAuth(), {
      wrapper: createQueryWrapper(),
    })

    await result.current.login({
      Username: '<EMAIL>',
      Password: 'password',
    })

    // Wait for prefetch to complete
    await waitFor(() => {
      expect(vi.mocked(userProfileApi.getUserInfo)).toHaveBeenCalled()
    })

    // Then manually fetch user info when needed (e.g., on profile page)
    const fetchedUserInfo = await userProfileApi.getUserInfo()

    // Should work when explicitly called
    expect(fetchedUserInfo).toEqual(userInfo)
  })
})
