import type {
  ExerciseModel,
  RecommendationModel,
  WorkoutLogSerieModel,
  MultiUnityWeight,
} from '@/types'
import {
  generateWarmupSets,
  generatePyramidSets,
  generateReversePyramidSets,
} from './workoutSetGenerators'
import {
  generateBackOffSets,
  generateRestPauseSet,
  generateNormalSets,
} from './workoutSetGenerators2'

export interface WorkoutLogSerieModelRef extends WorkoutLogSerieModel {
  SetTitle?: string
  LastTimeSet?: string
  IsHeaderCell?: boolean
  HeaderImage?: string
  HeaderTitle?: string
  ExerciseName?: string
  IsLastWarmupSet?: boolean
  IsFirstWorkSet?: boolean
  IsLastSet?: boolean
  IsNormalset?: boolean
  IsBackOffSet?: boolean
  BackColor?: string
  Increments?: MultiUnityWeight
  Min?: MultiUnityWeight
  Max?: MultiUnityWeight
  BodypartId?: number
  IsUnilateral?: boolean
  IsFlexibility?: boolean
  IsBodyweight?: boolean
  IsTimeBased?: boolean
  EquipmentId?: number
  NbPause?: number
}

/**
 * Creates workout sets based on exercise and recommendation model
 * Mimics the mobile app's CreateWorkoutSets functionality
 */
export function createWorkoutSets(
  exercise: ExerciseModel,
  recommendation: RecommendationModel,
  isKg: boolean
): WorkoutLogSerieModelRef[] {
  const setList: WorkoutLogSerieModelRef[] = []

  // 1. Generate Warm-up Sets
  const warmupSets = generateWarmupSets(exercise, recommendation)
  setList.push(...warmupSets)

  // 2. Generate Work Sets based on type
  let workSets: WorkoutLogSerieModelRef[] = []

  if (recommendation.IsPyramid) {
    workSets = generatePyramidSets(
      exercise,
      recommendation,
      setList.length,
      isKg
    )
  } else if (recommendation.IsReversePyramid) {
    workSets = generateReversePyramidSets(
      exercise,
      recommendation,
      setList.length,
      isKg
    )
  } else if (recommendation.IsBackOffSet) {
    workSets = generateBackOffSets(
      exercise,
      recommendation,
      setList.length,
      isKg
    )
  } else if (recommendation.NbPauses > 0) {
    workSets = generateRestPauseSet(
      exercise,
      recommendation,
      setList.length,
      isKg
    )
  } else {
    workSets = generateNormalSets(
      exercise,
      recommendation,
      setList.length,
      isKg
    )
  }

  setList.push(...workSets)

  // 3. Update IsNext property based on finished sets
  const nextSetIndex = setList.findIndex((set) => !set.IsFinished)
  if (nextSetIndex !== -1 && setList[nextSetIndex]) {
    setList[nextSetIndex].IsNext = true
  }

  // 4. Set additional properties for all sets
  setList.forEach((set) => {
    set.IsBodyweight = exercise.IsBodyweight
    set.IsTimeBased = exercise.IsTimeBased
    set.IsUnilateral = exercise.IsUnilateral
    set.BodypartId = exercise.BodyPartId
    set.Increments = recommendation.Increments
    set.Min = recommendation.Min
    set.Max = recommendation.Max
    set.IsFlexibility = exercise.IsFlexibility
    set.EquipmentId = exercise.EquipmentId
  })

  return setList
}

// Re-export helpers for convenience
export { formatWeight, roundToNearestIncrement } from './workoutSetHelpers'
