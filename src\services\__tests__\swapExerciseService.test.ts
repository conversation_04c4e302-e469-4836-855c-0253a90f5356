/**
 * Tests for SwapExerciseService
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { swapExerciseService } from '../swapExerciseService'
import type { ExerciseModel, WorkoutTemplateModel } from '@/types'
import type { ExerciseSwapContext } from '@/stores/workoutStore/types'

// Mock the API
vi.mock('@/api/workouts/template', () => ({
  replaceExerciseInWorkout: vi.fn(),
}))

describe('SwapExerciseService', () => {
  const mockSourceExercise: ExerciseModel = {
    Id: 1,
    Label: 'Bench Press',
    BodyPartId: 1,
    IsSystemExercise: true,
    IsSwapTarget: false,
    IsFinished: false,
    IsUnilateral: false,
    IsTimeBased: false,
    EquipmentId: 1,
    IsEasy: false,
    IsMedium: true,
    IsBodyweight: false,
    VideoUrl: '',
    IsNextExercise: false,
    IsPlate: true,
    IsWeighted: true,
    IsPyramid: false,
    RepsMaxValue: 10,
    RepsMinValue: 6,
    Timer: undefined,
    IsNormalSets: true,
    WorkoutGroupId: undefined,
    IsBodypartPriority: false,
    IsFlexibility: false,
    IsOneHanded: false,
    LocalVideo: '',
    IsAssisted: false,
    SetStyle: 'Normal',
  }

  const mockTargetExercise: ExerciseModel = {
    ...mockSourceExercise,
    Id: 2,
    Label: 'Dumbbell Press',
    EquipmentId: 2,
    IsPlate: false,
  }

  const mockWorkout: WorkoutTemplateModel = {
    Id: 100,
    UserId: 'test-user',
    Label: 'Test Workout',
    Exercises: [mockSourceExercise],
    IsSystemExercise: true,
    WorkoutSettingsModel: {},
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('createSwapContext', () => {
    it('should create a valid swap context', () => {
      const workoutId = 100
      const context = swapExerciseService.createSwapContext(
        workoutId,
        mockSourceExercise,
        mockTargetExercise
      )

      expect(context).toEqual({
        workoutId,
        sourceExerciseId: mockSourceExercise.Id,
        sourceBodyPartId: mockSourceExercise.BodyPartId,
        targetExerciseId: mockTargetExercise.Id,
        bodyPartId: mockTargetExercise.BodyPartId,
        label: mockTargetExercise.Label,
        isSystemExercise: mockTargetExercise.IsSystemExercise,
        isSwapTarget: true,
        isFinished: false,
        swapDate: expect.any(String),
        originalExercise: {
          label: mockSourceExercise.Label,
          bodyPartId: mockSourceExercise.BodyPartId,
          isSystemExercise: mockSourceExercise.IsSystemExercise,
          equipmentId: mockSourceExercise.EquipmentId,
          setStyle: mockSourceExercise.SetStyle,
          isBodyweight: mockSourceExercise.IsBodyweight,
          isFlexibility: mockSourceExercise.IsFlexibility,
        },
      })

      // Verify swapDate is a valid ISO string
      expect(new Date(context.swapDate).toISOString()).toBe(context.swapDate)
    })
  })

  describe('applySwapsToWorkout', () => {
    it('should apply swaps to workout exercises', () => {
      const swaps: Record<number, ExerciseSwapContext> = {
        [mockSourceExercise.Id]: {
          workoutId: 100,
          sourceExerciseId: mockSourceExercise.Id,
          sourceBodyPartId: mockSourceExercise.BodyPartId || 0,
          targetExerciseId: mockTargetExercise.Id,
          bodyPartId: mockTargetExercise.BodyPartId || 0,
          label: mockTargetExercise.Label,
          isSystemExercise: mockTargetExercise.IsSystemExercise || false,
          isSwapTarget: true,
          isFinished: false,
          swapDate: new Date().toISOString(),
        },
      }

      const result = swapExerciseService.applySwapsToWorkout(mockWorkout, swaps)

      expect(result.Exercises).toHaveLength(1)
      expect(result.Exercises[0]).toEqual({
        ...mockSourceExercise,
        Id: mockTargetExercise.Id,
        Label: mockTargetExercise.Label,
        BodyPartId: mockTargetExercise.BodyPartId,
        IsSwapTarget: true,
        IsSystemExercise: mockTargetExercise.IsSystemExercise,
      })
    })

    it('should return unchanged workout when no swaps exist', () => {
      const result = swapExerciseService.applySwapsToWorkout(mockWorkout, {})
      expect(result).toEqual(mockWorkout)
    })

    it('should handle workout without exercises', () => {
      const emptyWorkout = { ...mockWorkout, Exercises: [] }
      const swaps = { 1: {} as ExerciseSwapContext }

      const result = swapExerciseService.applySwapsToWorkout(
        emptyWorkout,
        swaps
      )
      expect(result).toEqual(emptyWorkout)
    })
  })

  describe('isExerciseSwapped', () => {
    it('should return true for swapped exercise', () => {
      const swaps = {
        1: {} as ExerciseSwapContext,
      }

      expect(swapExerciseService.isExerciseSwapped(1, swaps)).toBe(true)
    })

    it('should return false for non-swapped exercise', () => {
      const swaps = {
        1: {} as ExerciseSwapContext,
      }

      expect(swapExerciseService.isExerciseSwapped(2, swaps)).toBe(false)
    })
  })

  describe('getSwapForExercise', () => {
    it('should return swap context for matching workout and exercise', () => {
      const swapContext: ExerciseSwapContext = {
        workoutId: 100,
        sourceExerciseId: 1,
        sourceBodyPartId: 1,
        targetExerciseId: 2,
        bodyPartId: 1,
        label: 'Dumbbell Press',
        isSystemExercise: true,
        isSwapTarget: true,
        isFinished: false,
        swapDate: new Date().toISOString(),
      }

      const swaps = { 1: swapContext }

      const result = swapExerciseService.getSwapForExercise(100, 1, swaps)
      expect(result).toEqual(swapContext)
    })

    it('should return null for different workout ID', () => {
      const swapContext: ExerciseSwapContext = {
        workoutId: 100,
        sourceExerciseId: 1,
        sourceBodyPartId: 1,
        targetExerciseId: 2,
        bodyPartId: 1,
        label: 'Dumbbell Press',
        isSystemExercise: true,
        isSwapTarget: true,
        isFinished: false,
        swapDate: new Date().toISOString(),
      }

      const swaps = { 1: swapContext }

      const result = swapExerciseService.getSwapForExercise(200, 1, swaps)
      expect(result).toBeNull()
    })

    it('should return null for non-existent exercise', () => {
      const swaps = {}
      const result = swapExerciseService.getSwapForExercise(100, 1, swaps)
      expect(result).toBeNull()
    })
  })

  describe('replaceExerciseInArray', () => {
    it('should replace exercise in array maintaining position', () => {
      const exercises = [
        { ...mockSourceExercise, Id: 1 },
        { ...mockSourceExercise, Id: 2 },
        { ...mockSourceExercise, Id: 3 },
      ]

      const result = swapExerciseService.replaceExerciseInArray(
        exercises,
        2,
        mockTargetExercise
      )

      expect(result).toHaveLength(3)
      expect(result[0].Id).toBe(1)
      expect(result[1].Id).toBe(mockTargetExercise.Id)
      expect(result[1].Label).toBe(mockTargetExercise.Label)
      expect(result[1].IsSwapTarget).toBe(true)
      expect(result[2].Id).toBe(3)
    })

    it('should return unchanged array if exercise not found', () => {
      const exercises = [
        { ...mockSourceExercise, Id: 1 },
        { ...mockSourceExercise, Id: 2 },
      ]

      const result = swapExerciseService.replaceExerciseInArray(
        exercises,
        999, // Non-existent ID
        mockTargetExercise
      )

      expect(result).toEqual(exercises)
    })
  })

  describe('cleanupOldSwaps', () => {
    it('should remove swaps older than maxAge', () => {
      const oldDate = new Date(Date.now() - 31 * 24 * 60 * 60 * 1000) // 31 days ago
      const recentDate = new Date(Date.now() - 1 * 24 * 60 * 60 * 1000) // 1 day ago

      const swaps: Record<number, ExerciseSwapContext> = {
        1: {
          workoutId: 100,
          sourceExerciseId: 1,
          sourceBodyPartId: 1,
          targetExerciseId: 2,
          bodyPartId: 1,
          label: 'Old Swap',
          isSystemExercise: true,
          isSwapTarget: true,
          isFinished: false,
          swapDate: oldDate.toISOString(),
        },
        2: {
          workoutId: 100,
          sourceExerciseId: 2,
          sourceBodyPartId: 1,
          targetExerciseId: 3,
          bodyPartId: 1,
          label: 'Recent Swap',
          isSystemExercise: true,
          isSwapTarget: true,
          isFinished: false,
          swapDate: recentDate.toISOString(),
        },
      }

      const cleaned = swapExerciseService.cleanupOldSwaps(swaps)

      expect(Object.keys(cleaned)).toHaveLength(1)
      expect(cleaned[2]).toBeDefined()
      expect(cleaned[1]).toBeUndefined()
    })
  })
})
