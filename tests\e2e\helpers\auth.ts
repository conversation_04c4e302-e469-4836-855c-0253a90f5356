import { Page } from '@playwright/test'

export async function login(
  page: Page,
  email = '<EMAIL>',
  password = 'Dr123456'
) {
  // Fill email field using role selector
  await page.getByRole('textbox', { name: 'Email' }).fill(email)

  // Fill password field using ID selector (password inputs don't have textbox role)
  await page.locator('#password').fill(password)

  // Wait for form validation to complete and button to be enabled
  await page.waitForFunction(
    () => {
      const button = document.querySelector(
        'button[type="submit"]'
      ) as HTMLButtonElement
      return button && !button.disabled
    },
    { timeout: 10000 }
  )

  // Use more specific selector and force click to bypass any potential overlay issues
  const loginButton = page.locator('button[type="submit"]')
  await loginButton.waitFor({ state: 'visible', timeout: 10000 })
  await loginButton.click({ force: true, timeout: 10000 })
}

export async function mockSuccessfulLogin(page: Page) {
  // Mock the login API
  await page.route('**/api/Account/Login*', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        Token: 'mock-jwt-token',
        RefreshToken: 'mock-refresh-token',
        User: {
          Email: '<EMAIL>',
          FirstName: 'Test',
          LastName: 'User',
        },
      }),
    })
  })

  // Set up authentication state
  await page.addInitScript(() => {
    const authState = {
      state: {
        user: {
          email: '<EMAIL>',
          FirstName: 'Test',
          LastName: 'User',
        },
        isAuthenticated: true,
        token: 'mock-jwt-token',
        refreshToken: 'mock-refresh-token',
        cachedUserInfo: null,
        cacheVersion: 1,
      },
      version: 0,
    }
    window.localStorage.setItem('drmuscle-auth', JSON.stringify(authState))
  })
}
