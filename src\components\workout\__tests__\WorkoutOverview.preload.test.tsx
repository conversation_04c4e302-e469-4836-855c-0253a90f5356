import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { vi } from 'vitest'
import { WorkoutOverview } from '@/components/workout/WorkoutOverview'
import { useRouter } from 'next/navigation'
import { useWorkout } from '@/hooks/useWorkout'
import type { WorkoutTemplateGroupModel } from '@/types'

// Mock dependencies
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}))

vi.mock('@/hooks/useWorkout', () => ({
  useWorkout: vi.fn(),
}))

vi.mock('@/hooks/usePullToRefresh', () => ({
  usePullToRefresh: vi.fn(() => ({
    pullDistance: 0,
    isRefreshing: false,
    isPulling: false,
  })),
}))

describe('WorkoutOverview - Preload Tests', () => {
  const mockPush = vi.fn()
  const mockStartWorkout = vi.fn()
  const mockLoadExerciseRecommendation = vi.fn()

  // Create mock workout data
  const mockWorkoutGroup: WorkoutTemplateGroupModel = {
    Id: 1,
    Label: 'Test Workout',
    WorkoutTemplates: [
      {
        Id: 1,
        Label: 'Day 1',
        Exercises: [
          {
            Id: 100,
            Label: 'Barbell Squat',
            BodyPartId: 1,
            IsFinished: false,
            IsBodyweight: false,
            EquipmentId: 1,
            SetStyle: 'Normal',
            IsFlexibility: false,
            IsUnilateral: false,
            IsTimeBased: false,
            IsEasy: false,
            IsMedium: true,
            VideoUrl: '',
            IsNextExercise: true,
            IsPlate: false,
            IsWeighted: true,
            IsPyramid: false,
            RepsMaxValue: 12,
            RepsMinValue: 8,
            Timer: undefined,
            IsNormalSets: true,
            WorkoutGroupId: 1,
            IsBodypartPriority: false,
            IsOneHanded: false,
            LocalVideo: '',
            IsAssisted: false,
            IsSwapTarget: false,
            IsSystemExercise: true,
          },
          {
            Id: 101,
            Label: 'Bench Press',
            BodyPartId: 2,
            IsFinished: false,
            IsBodyweight: false,
            EquipmentId: 1,
            SetStyle: 'Normal',
            IsFlexibility: false,
            IsUnilateral: false,
            IsTimeBased: false,
            IsEasy: false,
            IsMedium: true,
            VideoUrl: '',
            IsNextExercise: false,
            IsPlate: false,
            IsWeighted: true,
            IsPyramid: false,
            RepsMaxValue: 10,
            RepsMinValue: 6,
            Timer: undefined,
            IsNormalSets: true,
            WorkoutGroupId: 1,
            IsBodypartPriority: false,
            IsOneHanded: false,
            LocalVideo: '',
            IsAssisted: false,
            IsSwapTarget: false,
            IsSystemExercise: true,
          },
        ],
        WorkoutSeconds: 0,
        WorkoutTemplateLevel: 1,
        NbReferenceSecondsAddedToTotal: 0,
        NbReferenceSecondsRemovedFromTotal: 0,
        SinceLastTime: false,
        StartTime: new Date().toISOString(),
        Id2: '',
        WorkoutsCount: 0,
        IsNormalWorkout: true,
        MesoCycle: 1,
        MicroCycle: 1,
        WorkoutsPerWeek: 3,
        HasBeenStarted: false,
        UserCustomName: '',
        UseLadders: false,
        RecommendedCountdown: 180,
        IsChallengeWorkout: false,
        IsQuickAddEdit: false,
        ChallengeName: null,
        ChallengeId: null,
        ChallengeDuration: null,
        ChallengeLog: null,
        UseAdvancedRestTimer: false,
        HasAccessedTimer: false,
        TimerDuration: 0,
        FirstWorkoutId: 0,
        TimeZoneInfoId: '',
      },
    ],
    IsFeaturedProgram: false,
    UserId: 'test-user',
    IsSystemExercise: true,
    RequiredWorkoutToLevelUp: 5,
    ProgramId: 1,
  }

  const defaultMockWorkout = {
    todaysWorkout: [mockWorkoutGroup],
    isLoadingWorkout: false,
    workoutError: null,
    startWorkout: mockStartWorkout,
    userProgramInfo: null,
    exercises: mockWorkoutGroup.WorkoutTemplates[0].Exercises,
    exerciseWorkSetsModels: [],
    expectedExerciseCount: 2,
    hasInitialData: true,
    isLoadingFresh: false,
    isOffline: false,
    refreshWorkout: vi.fn(),
    updateExerciseWorkSets: vi.fn(),
    workoutSession: null,
    finishWorkout: vi.fn(),
    isLoading: false,
    loadExerciseRecommendation: mockLoadExerciseRecommendation,
  }

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useRouter as jest.Mock).mockReturnValue({
      push: mockPush,
      replace: vi.fn(),
    })
    ;(useWorkout as jest.Mock).mockReturnValue(defaultMockWorkout)
  })

  describe('Start Workout Button', () => {
    it('should wait for workout to start and preload to complete before navigating', async () => {
      const user = userEvent.setup()

      // Mock startWorkout to simulate async preloading
      let preloadComplete = false
      mockStartWorkout.mockImplementation(async () => {
        // Simulate preloading delay
        await new Promise((resolve) => setTimeout(resolve, 100))
        preloadComplete = true
        return true
      })

      render(<WorkoutOverview />)

      const startButton = screen.getByRole('button', {
        name: /start a new workout session/i,
      })

      // Click start workout
      await user.click(startButton)

      // Verify navigation doesn't happen immediately
      expect(mockPush).not.toHaveBeenCalled()

      // Wait for preload to complete
      await waitFor(() => {
        expect(preloadComplete).toBe(true)
      })

      // Now navigation should happen
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/workout/exercise/100')
      })

      // Verify startWorkout was called with correct data
      expect(mockStartWorkout).toHaveBeenCalledWith([mockWorkoutGroup])
    })

    it('should show loading state during preload', async () => {
      const user = userEvent.setup()

      // Mock slow preloading
      mockStartWorkout.mockImplementation(async () => {
        await new Promise((resolve) => setTimeout(resolve, 200))
        return true
      })

      render(<WorkoutOverview />)

      const startButton = screen.getByRole('button', {
        name: /start a new workout session/i,
      })

      // Click start workout
      await user.click(startButton)

      // Button should be disabled during loading
      expect(startButton).toBeDisabled()

      // Wait for loading to complete
      await waitFor(
        () => {
          expect(startButton).toBeEnabled()
        },
        { timeout: 300 }
      )
    })

    it('should handle preload errors gracefully', async () => {
      const user = userEvent.setup()

      // Mock startWorkout to fail
      mockStartWorkout.mockRejectedValue(new Error('Preload failed'))

      render(<WorkoutOverview />)

      const startButton = screen.getByRole('button', {
        name: /start a new workout session/i,
      })

      // Click start workout
      await user.click(startButton)

      // Wait for button to be re-enabled after error
      await waitFor(() => {
        expect(startButton).toBeEnabled()
      })

      // Navigation should not happen on error
      expect(mockPush).not.toHaveBeenCalled()

      // Verify startWorkout was attempted
      expect(mockStartWorkout).toHaveBeenCalled()
    })
  })

  describe('Exercise Click', () => {
    it('should preload exercise recommendation before navigating', async () => {
      const user = userEvent.setup()

      // Mock successful workout start
      mockStartWorkout.mockResolvedValue(true)

      // Mock successful preload
      mockLoadExerciseRecommendation.mockResolvedValue({
        Id: 1,
        ExerciseId: 100,
        Reps: 10,
        Weight: 50,
      })

      render(<WorkoutOverview />)

      // Find and click first exercise card
      const exerciseCards = screen.getAllByRole('button', { name: /Exercise:/ })
      const firstExerciseCard = exerciseCards[0]
      await user.click(firstExerciseCard)

      // Verify preload was called before navigation
      await waitFor(() => {
        expect(mockLoadExerciseRecommendation).toHaveBeenCalledWith(100)
      })

      // Then navigation should happen
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/workout/exercise/100')
      })
    })

    it('should start workout if not started before preloading', async () => {
      const user = userEvent.setup()

      // Mock successful workout start
      mockStartWorkout.mockResolvedValue(true)

      render(<WorkoutOverview />)

      // Find and click first exercise card
      const exerciseCards = screen.getAllByRole('button', { name: /Exercise:/ })
      const firstExerciseCard = exerciseCards[0]
      await user.click(firstExerciseCard)

      // Verify workout starts first
      await waitFor(() => {
        expect(mockStartWorkout).toHaveBeenCalledWith([mockWorkoutGroup])
      })

      // Then preload happens
      await waitFor(() => {
        expect(mockLoadExerciseRecommendation).toHaveBeenCalledWith(100)
      })

      // Finally navigation
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/workout/exercise/100')
      })
    })

    it('should navigate even if preload fails', async () => {
      const user = userEvent.setup()

      // Mock successful workout start
      mockStartWorkout.mockResolvedValue(true)

      // Mock preload failure
      mockLoadExerciseRecommendation.mockRejectedValue(new Error('API Error'))

      render(<WorkoutOverview />)

      // Find and click first exercise card
      const exerciseCards = screen.getAllByRole('button', { name: /Exercise:/ })
      const firstExerciseCard = exerciseCards[0]
      await user.click(firstExerciseCard)

      // Wait for preload attempt
      await waitFor(() => {
        expect(mockLoadExerciseRecommendation).toHaveBeenCalledWith(100)
      })

      // Navigation should still happen despite preload failure
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/workout/exercise/100')
      })
    })
  })
})
