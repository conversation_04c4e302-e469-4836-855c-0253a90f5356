import React from 'react'
import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ExerciseSetsGrid } from '../ExerciseSetsGrid'
import type { ExerciseModel, RecommendationModel } from '@/types'

// Mock the NavigationContext
vi.mock('@/contexts/NavigationContext', () => ({
  useNavigation: () => ({
    setTitle: vi.fn(),
    setShowBackButton: vi.fn(),
  }),
}))

describe('ExerciseSetsGrid - Set Type Display', () => {
  const mockExercise: ExerciseModel = {
    Id: 1,
    Label: 'Bench Press',
    IsBodyweight: false,
    IsSystemExercise: true,
    IsSwapTarget: false,
    IsFinished: false,
    IsUnilateral: false,
    IsTimeBased: false,
    IsEasy: false,
    IsMedium: true,
    VideoUrl: '',
    IsNextExercise: false,
    IsPlate: true,
    IsWeighted: true,
    IsPyramid: false,
    IsNormalSets: true,
    Timer: 0,
    LocalVideo: '',
    IsAssisted: false,
  }

  beforeEach(() => {
    // Clear any console errors before each test
    vi.clearAllMocks()
  })

  const mockRestPauseRecommendation: RecommendationModel = {
    ExerciseId: 1,
    Series: 3,
    Reps: 10,
    Weight: { Lb: 135, Kg: 61.23 },
    WarmupsCount: 0,
    RpRest: 15,
    NbPauses: 2,
    NbRepsPauses: 3,
    IsNormalSets: false,
    IsDropSet: false,
    IsBackOffSet: false,
    IsPyramid: false,
    IsReversePyramid: false,
  }

  const mockDropSetRecommendation: RecommendationModel = {
    ...mockRestPauseRecommendation,
    IsNormalSets: true,
    NbPauses: 0,
    IsDropSet: true,
  }

  const mockPyramidRecommendation: RecommendationModel = {
    ...mockRestPauseRecommendation,
    IsNormalSets: true,
    NbPauses: 0,
    IsPyramid: true,
  }

  it('should display Rest-pause badge for rest-pause sets', () => {
    render(
      <ExerciseSetsGrid
        exercise={mockExercise}
        recommendation={mockRestPauseRecommendation}
        onSetUpdate={vi.fn()}
      />
    )

    // Rest-pause creates only one set with multiple pauses
    const badge = screen.getByRole('button', {
      name: /Rest-pause set type/i,
    })
    expect(badge).toBeInTheDocument()
    expect(badge).toHaveTextContent('Rest-pause')
  })

  it('should display Drop set badge for drop sets', () => {
    render(
      <ExerciseSetsGrid
        exercise={mockExercise}
        recommendation={mockDropSetRecommendation}
        onSetUpdate={vi.fn()}
      />
    )

    const badges = screen.getAllByRole('button', { name: /Drop set set type/i })
    expect(badges.length).toBe(mockDropSetRecommendation.Series)
    expect(badges[0]).toHaveTextContent('Drop set')
  })

  it('should display Pyramid badge for pyramid sets', () => {
    render(
      <ExerciseSetsGrid
        exercise={mockExercise}
        recommendation={mockPyramidRecommendation}
        onSetUpdate={vi.fn()}
      />
    )

    const badges = screen.getAllByRole('button', { name: /Pyramid set type/i })
    expect(badges.length).toBe(1) // Only first pyramid set should have badge
    expect(badges[0]).toHaveTextContent('Pyramid')
  })

  it('should not display badge for normal sets', () => {
    const normalRecommendation: RecommendationModel = {
      ...mockRestPauseRecommendation,
      IsNormalSets: true,
      NbPauses: 0,
    }

    render(
      <ExerciseSetsGrid
        exercise={mockExercise}
        recommendation={normalRecommendation}
        onSetUpdate={vi.fn()}
      />
    )

    const badges = screen.queryAllByRole('button', { name: /set type/i })
    expect(badges).toHaveLength(0)
  })
})
