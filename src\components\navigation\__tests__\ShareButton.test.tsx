import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import { ShareButton } from '../ShareButton'

describe('ShareButton', () => {
  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()
  })

  describe('Web Share API Support', () => {
    it('should render share button with icon', () => {
      render(<ShareButton />)

      const button = screen.getByRole('button', { name: /share/i })
      expect(button).toBeInTheDocument()

      // Check for SVG icon
      const svg = button.querySelector('svg')
      expect(svg).toBeInTheDocument()
    })

    it('should render with label when showLabel is true', () => {
      render(<ShareButton showLabel />)

      const button = screen.getByRole('button', { name: /share/i })
      expect(button).toHaveTextContent('Share')
    })

    it('should apply custom className', () => {
      const customClass = 'custom-share-button'
      render(<ShareButton className={customClass} />)

      const button = screen.getByRole('button', { name: /share/i })
      expect(button).toHaveClass(customClass)
    })

    it('should call navigator.share with default data when clicked', async () => {
      const mockShare = vi.fn().mockResolvedValue(undefined)
      Object.defineProperty(navigator, 'share', {
        value: mockShare,
        writable: true,
        configurable: true,
      })

      render(<ShareButton />)

      const button = screen.getByRole('button', { name: /share/i })
      fireEvent.click(button)

      await waitFor(() => {
        expect(mockShare).toHaveBeenCalledWith({
          title: 'Workout Complete!',
          text: 'I just completed my workout with Dr. Muscle X!',
        })
      })
    })

    it('should call navigator.share with custom data when provided', async () => {
      const mockShare = vi.fn().mockResolvedValue(undefined)
      Object.defineProperty(navigator, 'share', {
        value: mockShare,
        writable: true,
        configurable: true,
      })

      const customShareData = {
        title: 'Custom Title',
        text: 'Custom share message with workout stats',
      }

      render(<ShareButton shareData={customShareData} />)

      const button = screen.getByRole('button', { name: /share/i })
      fireEvent.click(button)

      await waitFor(() => {
        expect(mockShare).toHaveBeenCalledWith(customShareData)
      })
    })

    it('should handle share cancellation gracefully', async () => {
      const mockShare = vi.fn().mockRejectedValue(new Error('Share cancelled'))
      Object.defineProperty(navigator, 'share', {
        value: mockShare,
        writable: true,
        configurable: true,
      })

      render(<ShareButton />)

      const button = screen.getByRole('button', { name: /share/i })

      // Should not throw error when share is cancelled
      expect(() => fireEvent.click(button)).not.toThrow()
    })

    it('should handle share API errors gracefully', async () => {
      const mockShare = vi.fn().mockRejectedValue(new Error('Share failed'))
      Object.defineProperty(navigator, 'share', {
        value: mockShare,
        writable: true,
        configurable: true,
      })

      render(<ShareButton />)

      const button = screen.getByRole('button', { name: /share/i })

      // Should not throw error when share fails
      expect(() => fireEvent.click(button)).not.toThrow()
    })
  })

  describe('Integration with WorkoutComplete', () => {
    it('should format share message with workout stats', () => {
      const workoutShareData = {
        title: 'Workout Complete!',
        text: 'I just completed my workout with Dr. Muscle X! 3 exercises, 12 sets, 5,250 lbs lifted. 8 weeks streak! 💪',
      }

      const mockShare = vi.fn().mockResolvedValue(undefined)
      Object.defineProperty(navigator, 'share', {
        value: mockShare,
        writable: true,
        configurable: true,
      })

      render(<ShareButton shareData={workoutShareData} showLabel />)

      const button = screen.getByRole('button', { name: /share/i })
      fireEvent.click(button)

      expect(mockShare).toHaveBeenCalledWith(workoutShareData)
    })
  })

  describe('Accessibility', () => {
    it('should have proper aria-label', () => {
      render(<ShareButton />)

      const button = screen.getByRole('button', { name: /share/i })
      expect(button).toHaveAttribute('aria-label', 'Share')
    })

    it('should be keyboard accessible', () => {
      render(<ShareButton />)

      const button = screen.getByRole('button', { name: /share/i })

      // Focus the button
      button.focus()
      expect(document.activeElement).toBe(button)

      // Trigger with Enter key
      fireEvent.keyDown(button, { key: 'Enter' })
      // Button should handle keyboard activation
    })
  })
})
