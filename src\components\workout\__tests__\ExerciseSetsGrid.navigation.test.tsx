import React from 'react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { SetScreenWithGrid } from '../SetScreenWithGrid'
import { NavigationProvider } from '@/contexts/NavigationContext'
import { useSetScreenLogic } from '@/hooks/useSetScreenLogic'
import type { ExerciseModel, RecommendationModel } from '@/types'

// Mock hooks
vi.mock('@/hooks/useSetScreenLogic')
vi.mock('@/utils/debugLog', () => ({
  debugLog: vi.fn(),
}))

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(() => ({
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
    back: vi.fn(),
  })),
}))

// Mock navigation context
const mockSetTitle = vi.fn()
vi.mock('@/contexts/NavigationContext', () => ({
  NavigationProvider: ({ children }: any) => <div>{children}</div>,
  useNavigation: () => ({
    title: '',
    setTitle: mockSetTitle,
    canGoBack: false,
    goBack: vi.fn(),
    push: vi.fn(),
    replace: vi.fn(),
  }),
}))

const mockExercise: ExerciseModel = {
  Id: 123,
  Label: 'Bench Press',
  IsBodyweight: false,
  IsSystemExercise: true,
  BodyPartId: 1,
}

const mockRecommendation: RecommendationModel = {
  Id: 1,
  Series: 3,
  Reps: 10,
  Weight: { Lb: 135, Kg: 61.23 },
  WarmupsCount: 2,
  WarmUpsList: [
    { WarmUpReps: 5, WarmUpWeightSet: { Lb: 95, Kg: 43.09 } },
    { WarmUpReps: 8, WarmUpWeightSet: { Lb: 115, Kg: 52.16 } },
  ],
  HistorySet: [],
  FirstWorkSet1RM: { Lb: 180, Kg: 81.65 },
  LastLogDate: '2024-01-15',
} as RecommendationModel

describe('Exercise Name in Navigation', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockSetTitle.mockClear()
  })

  it('should set exercise name in navigation title when component mounts', async () => {
    // Mock the hook to return exercise data
    vi.mocked(useSetScreenLogic).mockReturnValue({
      currentExercise: mockExercise,
      recommendation: mockRecommendation,
      currentSetIndex: 0,
      completedSets: [],
      isLoading: false,
      error: null,
      showComplete: false,
      showExerciseComplete: false,
      showRIRPicker: false,
      isSaving: false,
      saveError: null,
      isTransitioning: false,
      isLastExercise: false,
      exercises: [mockExercise],
      currentExerciseIndex: 0,
      setSetData: vi.fn(),
      handleSaveSet: vi.fn(),
      handleRIRSelect: vi.fn(),
      handleRIRCancel: vi.fn(),
      refetchRecommendation: vi.fn(),
    } as any)

    // Render the component
    render(
      <NavigationProvider>
        <SetScreenWithGrid exerciseId={123} />
      </NavigationProvider>
    )

    // Wait for the effect to run and verify setTitle was called
    await waitFor(() => {
      expect(mockSetTitle).toHaveBeenCalledWith('Bench Press')
    })
  })

  it('should not set title when there is no exercise', async () => {
    // Mock the hook to return no exercise
    vi.mocked(useSetScreenLogic).mockReturnValue({
      currentExercise: null,
      recommendation: null,
      currentSetIndex: 0,
      completedSets: [],
      isLoading: false,
      error: null,
      showComplete: false,
      showExerciseComplete: false,
      showRIRPicker: false,
      isSaving: false,
      saveError: null,
      isTransitioning: false,
      isLastExercise: false,
      exercises: [],
      currentExerciseIndex: 0,
      setSetData: vi.fn(),
      handleSaveSet: vi.fn(),
      handleRIRSelect: vi.fn(),
      handleRIRCancel: vi.fn(),
      refetchRecommendation: vi.fn(),
    } as any)

    render(
      <NavigationProvider>
        <SetScreenWithGrid exerciseId={123} />
      </NavigationProvider>
    )

    // Wait a bit to ensure effect runs
    await waitFor(() => {
      expect(mockSetTitle).not.toHaveBeenCalled()
    })
  })

  it('should update navigation title when exercise changes', async () => {
    const { rerender } = render(
      <NavigationProvider>
        <SetScreenWithGrid exerciseId={123} />
      </NavigationProvider>
    )

    // Change to a different exercise
    const newExercise = { ...mockExercise, Id: 456, Label: 'Squat' }

    vi.mocked(useSetScreenLogic).mockReturnValue({
      currentExercise: newExercise,
      recommendation: mockRecommendation,
      currentSetIndex: 0,
      completedSets: [],
      isLoading: false,
      error: null,
      showComplete: false,
      showExerciseComplete: false,
      showRIRPicker: false,
      isSaving: false,
      saveError: null,
      isTransitioning: false,
      isLastExercise: false,
      exercises: [newExercise],
      currentExerciseIndex: 0,
      setSetData: vi.fn(),
      handleSaveSet: vi.fn(),
      handleRIRSelect: vi.fn(),
      handleRIRCancel: vi.fn(),
      refetchRecommendation: vi.fn(),
    } as any)

    rerender(
      <NavigationProvider>
        <SetScreenWithGrid exerciseId={456} />
      </NavigationProvider>
    )

    // The navigation title should be updated to the new exercise
    await waitFor(() => {
      expect(mockSetTitle).toHaveBeenCalledWith('Squat')
    })
  })
})

describe('Arrow Tap Functionality', () => {
  let mockSetSetData: ReturnType<typeof vi.fn>

  beforeEach(() => {
    vi.clearAllMocks()
    mockSetSetData = vi.fn()

    // Mock the hook with a basic setup
    vi.mocked(useSetScreenLogic).mockReturnValue({
      currentExercise: mockExercise,
      recommendation: mockRecommendation,
      currentSetIndex: 0,
      completedSets: [],
      isLoading: false,
      error: null,
      showComplete: false,
      showExerciseComplete: false,
      showRIRPicker: false,
      isSaving: false,
      saveError: null,
      isTransitioning: false,
      isLastExercise: false,
      exercises: [mockExercise],
      currentExerciseIndex: 0,
      setSetData: mockSetSetData,
      handleSaveSet: vi.fn(),
      handleRIRSelect: vi.fn(),
      handleRIRCancel: vi.fn(),
      refetchRecommendation: vi.fn(),
    } as any)
  })

  it('should increase reps when up arrow is tapped on active set', async () => {
    render(
      <NavigationProvider>
        <SetScreenWithGrid exerciseId={123} />
      </NavigationProvider>
    )

    // Wait for the component to render
    await waitFor(() => {
      // Should show warmup sets as 'W'
      const warmupSets = screen.getAllByText('W')
      expect(warmupSets.length).toBe(2) // 2 warmup sets
    })

    // Find the up arrow for reps on the active set (only active sets have arrow buttons)
    const repsUpArrow = screen.getByLabelText('Increase reps')

    // Click the up arrow
    fireEvent.click(repsUpArrow)

    // Verify setSetData was called
    expect(mockSetSetData).toHaveBeenCalled()
  })

  it('should increase weight when up arrow is tapped on active set', async () => {
    render(
      <NavigationProvider>
        <SetScreenWithGrid exerciseId={123} />
      </NavigationProvider>
    )

    // Wait for the component to render
    await waitFor(() => {
      const warmupSets = screen.getAllByText('W')
      expect(warmupSets.length).toBe(2)
    })

    // Find the up arrow for weight on the active set
    const weightUpArrow = screen.getByLabelText('Increase weight')

    // Click the up arrow
    fireEvent.click(weightUpArrow)

    // Verify setSetData was called
    expect(mockSetSetData).toHaveBeenCalled()
  })

  it('should not show arrow buttons on inactive sets', async () => {
    // Mock with a completed first set
    vi.mocked(useSetScreenLogic).mockReturnValue({
      currentExercise: mockExercise,
      recommendation: mockRecommendation,
      currentSetIndex: 1, // Second set is active
      completedSets: [
        {
          Id: 1,
          SetNo: '1',
          Reps: 5,
          Weight: { Lb: 95, Kg: 43 },
          IsFinished: true,
          IsWarmups: true,
        },
      ],
      isLoading: false,
      error: null,
      showComplete: false,
      showExerciseComplete: false,
      showRIRPicker: false,
      isSaving: false,
      saveError: null,
      isTransitioning: false,
      isLastExercise: false,
      exercises: [mockExercise],
      currentExerciseIndex: 0,
      setSetData: vi.fn(),
      handleSaveSet: vi.fn(),
      handleRIRSelect: vi.fn(),
      handleRIRCancel: vi.fn(),
      refetchRecommendation: vi.fn(),
    } as any)

    render(
      <NavigationProvider>
        <SetScreenWithGrid exerciseId={123} />
      </NavigationProvider>
    )

    // There should be exactly 4 arrow buttons (2 up, 2 down) for the single active set
    const allArrows = screen.getAllByRole('button', {
      name: /Increase|Decrease/,
    })
    expect(allArrows).toHaveLength(4)
  })

  it('should handle arrow key increments correctly for kg vs lbs', async () => {
    const { ExerciseSetsGrid } = await import('../ExerciseSetsGrid')

    const mockOnSetUpdate = vi.fn()

    const sets = [
      {
        Id: 1,
        SetNo: '1',
        Reps: 10,
        Weight: { Lb: 100, Kg: 45 },
        IsNext: true,
        IsFinished: false,
        IsWarmups: false,
      },
    ]

    const { rerender } = render(
      <ExerciseSetsGrid
        exercise={mockExercise}
        sets={sets as any}
        recommendation={mockRecommendation}
        onSetUpdate={mockOnSetUpdate}
        unit="kg"
      />
    )

    // Click weight up arrow in kg mode
    const weightUpArrow = screen.getByLabelText('Increase weight')
    fireEvent.click(weightUpArrow)

    // Should increase by 2.5 kg
    expect(mockOnSetUpdate).toHaveBeenCalledWith(1, { weight: 47.5 })

    // Reset mock
    mockOnSetUpdate.mockClear()

    // Switch to lbs
    rerender(
      <ExerciseSetsGrid
        exercise={mockExercise}
        sets={sets as any}
        recommendation={mockRecommendation}
        onSetUpdate={mockOnSetUpdate}
        unit="lbs"
      />
    )

    // Click weight up arrow in lbs mode
    fireEvent.click(screen.getByLabelText('Increase weight'))

    // Should increase by 5 lbs
    expect(mockOnSetUpdate).toHaveBeenCalledWith(1, { weight: 105 })
  })
})
