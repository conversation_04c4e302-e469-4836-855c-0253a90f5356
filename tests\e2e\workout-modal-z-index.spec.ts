import { test, expect } from '@playwright/test'

test.describe('Workout Modal Z-Index', () => {
  test.beforeEach(async ({ page }) => {
    // Login
    await page.goto('/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'Dr123456')
    await page.click('button[type="submit"]')

    // Wait for navigation to program page
    await page.waitForURL('/program')
  })

  test('should display swap exercise modal above Continue Workout button', async ({
    page,
  }) => {
    // Navigate to workout
    await page.click('button:has-text("Continue to Workout")')
    await page.waitForURL('/workout')

    // Start workout if not already started
    const startButton = page.locator('button:has-text("Start Workout")')
    if (await startButton.isVisible()) {
      await startButton.click()
      await page.waitForTimeout(1000)
    }

    // Click on an exercise to go to exercise page
    await page.locator('.rounded-lg').first().click()
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Click swap exercise button
    await page.click('button:has-text("Swap")')

    // Wait for modal to appear
    const modal = page.locator('[role="dialog"][aria-label="Swap Exercise"]')
    await expect(modal).toBeVisible()

    // Get the modal backdrop
    const modalBackdrop = page.locator('[role="presentation"]')

    // Check that modal backdrop has higher z-index class
    const classList = await modalBackdrop.getAttribute('class')
    expect(classList).toContain('z-[60]')
    expect(classList).not.toContain('z-50')

    // Verify the Continue Workout button is not visible through the modal
    const continueButton = page.locator('button:has-text("Continue Workout")')

    // The modal should overlay the button, making it not clickable
    if (await continueButton.isVisible()) {
      // Get bounding boxes to verify positioning
      const modalBox = await modalBackdrop.boundingBox()
      const buttonBox = await continueButton.boundingBox()

      if (modalBox && buttonBox) {
        // Modal should cover the entire viewport including button area
        expect(modalBox.height).toBeGreaterThan(buttonBox.y + buttonBox.height)
      }
    }

    // Close modal
    await page.keyboard.press('Escape')
    await expect(modal).not.toBeVisible()
  })

  test('should display set logging modal above Continue Workout button', async ({
    page,
  }) => {
    // Navigate to workout
    await page.click('button:has-text("Continue to Workout")')
    await page.waitForURL('/workout')

    // Start workout if not already started
    const startButton = page.locator('button:has-text("Start Workout")')
    if (await startButton.isVisible()) {
      await startButton.click()
      await page.waitForTimeout(1000)
    }

    // Click on an exercise to go to exercise page
    await page.locator('.rounded-lg').first().click()
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Click log set button
    await page.click('button:has-text("LOG SET")')

    // Wait for modal to appear
    const modal = page.locator('[role="dialog"][aria-label="Log Set"]')
    await expect(modal).toBeVisible()

    // Get the modal backdrop
    const modalBackdrop = page.locator('[role="presentation"]')

    // Check that modal backdrop has higher z-index class
    const classList = await modalBackdrop.getAttribute('class')
    expect(classList).toContain('z-[60]')
    expect(classList).not.toContain('z-50')

    // Close modal
    await page.keyboard.press('Escape')
    await expect(modal).not.toBeVisible()
  })
})
