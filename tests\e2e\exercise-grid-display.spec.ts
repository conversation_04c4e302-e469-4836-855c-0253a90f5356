import { test, expect } from '@playwright/test'

// Helper function to perform login
async function login(page: any, email: string, password: string) {
  await page.getByRole('textbox', { name: 'Email' }).fill(email)
  await page.locator('#password').fill(password)
  await page.waitForFunction(
    () => {
      const button = document.querySelector(
        'button[type="submit"]'
      ) as HTMLButtonElement
      return button && !button.disabled
    },
    { timeout: 10000 }
  )
  const loginButton = page.locator('button[type="submit"]')
  await loginButton.waitFor({ state: 'visible', timeout: 10000 })
  await loginButton.click({ force: true, timeout: 10000 })
}

// Mobile-first configuration
test.use({
  viewport: { width: 375, height: 667 }, // iPhone SE
  hasTouch: true,
  isMobile: true,
})

test.describe('Exercise Grid Display', () => {
  test('should display all sets in a grid layout', async ({ page }) => {
    test.setTimeout(60000)

    // Login and navigate to workout
    await page.goto('/login')
    await login(page, '<EMAIL>', 'Dr123456')
    await page.waitForURL('/program', { timeout: 15000 })

    // Start or continue workout
    const continueButton = page.getByRole('button', { name: /continue/i })
    const startButton = page.getByRole('button', { name: /open workout/i })

    if (await continueButton.isVisible({ timeout: 2000 }).catch(() => false)) {
      await continueButton.click({ force: true })
    } else {
      await startButton.click({ force: true })
    }

    await page.waitForURL(/\/workout/, { timeout: 20000 })
    await page.waitForLoadState('networkidle')

    // Navigate to first exercise
    const exerciseCards = page.locator('[data-testid="exercise-card"]')
    await exerciseCards.first().click()
    await page.waitForURL(/\/workout\/exercise\/\d+/, { timeout: 15000 })
    await page.waitForLoadState('networkidle')

    // Wait for page to load
    await page.waitForTimeout(2000)

    // Debug: Log page content
    const pageTitle = await page.title()
    console.log('Page title:', pageTitle)

    // Check current URL
    console.log('Current URL:', page.url())

    // Check if exercise name is displayed
    const exerciseName = await page.locator('h1, h2').first().textContent()
    console.log('Exercise name:', exerciseName)

    // Check if grid header is visible (grid layout)
    const gridHeader = page
      .locator('.grid-cols-\\[25px_60px_1fr_25px_1fr\\]')
      .first()
    const hasGrid = await gridHeader.isVisible().catch(() => false)

    // Also check for the SetCell header
    const hasSetCell = await page
      .locator('text=SET')
      .isVisible()
      .catch(() => false)
    console.log('Has SET text:', hasSetCell)

    // Check for any input fields
    const inputCount = await page.locator('input[type="number"]').count()
    console.log('Number input count:', inputCount)

    // Check for table/grid structure
    const gridElements = await page.locator('.grid').count()
    console.log('Grid elements count:', gridElements)

    if (hasGrid) {
      console.log('Grid layout detected!')
      // Check grid header columns
      const headerTexts = await gridHeader.locator('div').allTextContents()
      console.log('Grid header columns:', headerTexts)
      expect(headerTexts).toContain('SET')
      expect(headerTexts).toContain('REPS')
    } else {
      console.log('Grid layout not found')
      // Take a screenshot to see what's displayed
      await page.screenshot({
        path: 'tests/screenshots/exercise-page-debug.png',
        fullPage: true,
      })
    }

    // Check for LBS or KG header
    const lbsHeader = page.locator('text=LBS')
    const kgHeader = page.locator('text=KG')
    const hasLbs = await lbsHeader.isVisible().catch(() => false)
    const hasKg = await kgHeader.isVisible().catch(() => false)
    expect(hasLbs || hasKg).toBe(true)

    // Check that input fields are present in grid
    const repsInputs = page.locator('input[aria-label*="Reps for set"]')
    const weightInputs = page.locator('input[aria-label*="Weight for set"]')

    const repsCount = await repsInputs.count()
    const weightCount = await weightInputs.count()

    console.log(
      `Found ${repsCount} reps inputs and ${weightCount} weight inputs`
    )

    // Should have at least 2 sets (minimum work sets)
    expect(repsCount).toBeGreaterThanOrEqual(2)
    expect(weightCount).toBeGreaterThanOrEqual(2)

    // Check for check/delete icons
    const checkIcons = page.locator('[data-testid="check-icon"]')
    const deleteIcons = page.locator('[data-testid="delete-icon"]')

    const hasCheckIcons = (await checkIcons.count()) > 0
    const hasDeleteIcons = (await deleteIcons.count()) > 0

    expect(hasCheckIcons || hasDeleteIcons).toBe(true)

    // Check for "Add set" button
    const addSetButton = page.locator('button:has-text("Add set")')
    await expect(addSetButton).toBeVisible()

    // Take screenshot for visual verification
    await page.screenshot({
      path: 'tests/screenshots/exercise-grid-display.png',
      fullPage: true,
    })

    console.log('Grid layout displayed successfully with all sets visible')
  })
})
