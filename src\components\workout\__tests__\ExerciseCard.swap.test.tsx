import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import React from 'react'
import { ExerciseCard } from '../ExerciseCard'
import type { ExerciseWorkSetsModel, WorkoutTemplateModel } from '@/types'

// Mock the stores
vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: vi.fn((selector) => {
    const state = {
      currentWorkout: null, // This is the issue we're testing
    }
    // If a selector is provided, call it with the state
    if (selector) {
      return selector(state)
    }
    // Otherwise return the whole state
    return state
  }),
}))

// Mock the modal component
vi.mock('../ExerciseSwapModal', () => ({
  ExerciseSwapModal: vi.fn((props) => {
    // The real component checks if isOpen is true
    if (!props.isOpen) return null
    return (
      <div data-testid="exercise-swap-modal">
        <button onClick={props.onClose}>Close</button>
      </div>
    )
  }),
}))

describe('ExerciseCard - Swap Functionality', () => {
  const mockExercise: ExerciseWorkSetsModel = {
    Id: 1,
    Label: 'Bench Press',
    BodyPartId: 1,
    IsBodyweight: false,
    IsFinished: false,
    IsNextExercise: false,
    isLoadingSets: false,
    setsError: null,
    lastSetsUpdate: Date.now(),
    sets: [
      { Id: 1, RepsValue: 10, WeightValue: 100, SetOrder: 1 },
      { Id: 2, RepsValue: 10, WeightValue: 100, SetOrder: 2 },
    ],
    IsSwapTarget: false,
    WorkoutSets: [],
    isLoading: false,
    error: null,
  }

  const mockHandlers = {
    onExerciseClick: vi.fn(),
    onRetry: vi.fn(),
  }

  const mockWorkout: WorkoutTemplateModel = {
    Id: 123,
    Name: 'Test Workout',
    Exercises: [mockExercise],
    IsSystemExercise: false,
    UserId: 'test-user',
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should not render swap modal when currentWorkout is null from store', () => {
    // Render without workout prop, so it will use store value (null)
    render(<ExerciseCard exercise={mockExercise} {...mockHandlers} />)

    // Modal should not be present initially
    expect(screen.queryByTestId('exercise-swap-modal')).not.toBeInTheDocument()

    // Find and click the swap button
    const swapButton = screen.getByRole('button', { name: /swap exercise/i })
    fireEvent.click(swapButton)

    // The modal should still not be rendered because currentWorkout is null
    expect(screen.queryByTestId('exercise-swap-modal')).not.toBeInTheDocument()
  })

  it('should render swap modal when workout is passed as prop', () => {
    render(
      <ExerciseCard
        exercise={mockExercise}
        workout={mockWorkout}
        {...mockHandlers}
      />
    )

    // Find and click the swap button
    const swapButton = screen.getByRole('button', { name: /swap exercise/i })
    fireEvent.click(swapButton)

    // The modal should be rendered
    expect(screen.getByTestId('exercise-swap-modal')).toBeInTheDocument()
  })

  it('should show user feedback when trying to swap without workout data', async () => {
    // This test will be implemented after we add the toast notification
    render(<ExerciseCard exercise={mockExercise} {...mockHandlers} />)

    const swapButton = screen.getByRole('button', { name: /swap exercise/i })
    fireEvent.click(swapButton)

    // After implementation, we should see a toast or alert
    // For now, just verify the button is clickable
    expect(swapButton).toBeEnabled()
  })

  it('should stop propagation when swap button is clicked', () => {
    render(<ExerciseCard exercise={mockExercise} {...mockHandlers} />)

    const swapButton = screen.getByRole('button', { name: /swap exercise/i })
    const mockEvent = { stopPropagation: vi.fn() }

    // Create and dispatch a click event with stopPropagation
    const clickEvent = new MouseEvent('click', { bubbles: true })
    Object.defineProperty(clickEvent, 'stopPropagation', {
      value: mockEvent.stopPropagation,
    })

    swapButton.dispatchEvent(clickEvent)

    // Verify onExerciseClick was not called (due to stopPropagation)
    expect(mockHandlers.onExerciseClick).not.toHaveBeenCalled()
  })
})
