import React from 'react'
import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { SetTypeBadge } from '../SetTypeBadge'

describe('SetTypeBadge', () => {
  it('should render badge for Normal set type', () => {
    render(<SetTypeBadge setType="Normal" />)
    expect(screen.getByText('Normal')).toBeInTheDocument()
  })

  it('should render badge for special set types', () => {
    render(<SetTypeBadge setType="Rest-pause" />)
    expect(screen.getByText('Rest-pause')).toBeInTheDocument()
  })

  it('should render abbreviated text for long set types', () => {
    render(<SetTypeBadge setType="Reverse pyramid" />)
    expect(screen.getByText('Rev pyramid')).toBeInTheDocument()
  })

  it('should have minimum touch target size of 44px', () => {
    render(<SetTypeBadge setType="Drop set" />)
    const badge = screen.getByRole('button')
    expect(badge).toHaveClass('min-h-[44px]')
  })

  it('should call onClick when tapped', () => {
    const handleClick = vi.fn()
    render(<SetTypeBadge setType="Pyramid" onClick={handleClick} />)

    const badge = screen.getByRole('button')
    fireEvent.click(badge)

    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('should have proper styling for mobile visibility', () => {
    render(<SetTypeBadge setType="Back-off" />)
    const badge = screen.getByRole('button')

    expect(badge).toHaveClass('text-xs')
    expect(badge).toHaveClass('font-medium')
    expect(badge).toHaveClass('rounded-full')
  })

  it('should use theme-aware colors', () => {
    render(<SetTypeBadge setType="Drop set" />)
    const badge = screen.getByRole('button')

    expect(badge).toHaveClass('bg-brand-primary/10')
    expect(badge).toHaveClass('text-brand-primary')
  })

  it('should have appropriate aria-label for accessibility', () => {
    render(<SetTypeBadge setType="Rest-pause" />)
    const badge = screen.getByRole('button')

    expect(badge).toHaveAttribute(
      'aria-label',
      'Rest-pause set type - tap for more info'
    )
  })
})
